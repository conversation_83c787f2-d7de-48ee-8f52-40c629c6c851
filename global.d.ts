declare module '*.less';

declare module '*.svg' {
  import * as React from 'react';
  export const ReactComponent: React.FunctionComponent<React.SVGProps<SVGSVGElement> & { title?: string }>;
  const src: string;
  export default src;
}

declare interface HTTP_RESPONSE {
  success: 1 | 0;
  data: any;
  message: string;
}

declare interface SERES_RESPONSE {
  msg: string;
  msgCode: string;
  resp: any;
  success: true | false;
  total: number;
  traceId: string | null;
}

//扩展em-emoji标签类型定义
// declare global {
declare namespace JSX {
  interface IntrinsicElements {
    'em-emoji': React.DetailedHTMLProps<
      React.HTMLAttributes<HTMLElement> & {
        id: string;
        size?: string;
        set?: string; //The emoji set: native, apple, facebook, google, twitter
      },
      HTMLElement
    >;
  }
}
// }

declare module 'butterfly-dag';
declare module '@bytemd/plugin-gfm';
declare module '@bytemd/react';

{
  "compilerOptions": {
    // 基础配置
    "target": "es5", // 兼容旧浏览器
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ], // 浏览器环境 + ES 最新特性
    "module": "esnext", // 与 Next.js 的模块系统匹配
    "moduleResolution": "node", // Node.js 风格的模块解析
    "resolveJsonModule": true, // 允许导入 JSON 文件
    "allowJs": true, // 允许混合 JS/TS 项目
    "skipLibCheck": true, // 跳过库类型检查（提升编译速度）
    // 类型检查严格性
    "strict": true, // 启用所有严格类型检查
    "noImplicitAny": false, // 禁止隐式 any 类型
    "strictNullChecks": true, // 严格的 null/undefined 检查
    "strictFunctionTypes": true, // 函数参数严格逆变检查
    "strictBindCallApply": true, // 严格的 bind/call/apply 检查
    "noUnusedLocals": false, // 检查未使用的局部变量
    "noUnusedParameters": false, // 检查未使用的函数参数
    // JSX 配置
    "jsx": "preserve", // 保留 JSX 结构（由 Next.js 处理转换）
    "jsxImportSource": "react", // 明确 React 的 JSX 来源
    // 路径映射（根据项目结构调整）
    "baseUrl": ".", // 基础路径
    "paths": {
      "@/*": [
        "./src/*"
      ], // 示例路径别名（推荐）
      "@/components/*": [
        "./src/components/*"
      ],
      "@/utils/*": [
        "./src/utils/*"
      ]
    },
    // 输出配置
    "esModuleInterop": true, // 兼容 CommonJS/ES 模块
    "forceConsistentCasingInFileNames": true, // 强制文件名大小写一致
    "isolatedModules": true, // Babel 必需配置（Next.js 使用 Babel 转译 TS）
    "noEmit": true, // 不生成输出文件（Next.js 负责编译）
    // 类型声明'next/types/global'
    "types": [
      "node",
      "next"
    ], // Next.js 类型支持
    "typeRoots": [
      "node_modules/@types",
      "./types"
    ], // 自定义类型目录
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "next-env.d.ts", // Next.js 环境类型
    "**/*.ts", // 所有 TypeScript 文件
    "**/*.tsx", // 所有 TypeScript JSX 文件
    "types/**/*.d.ts", // 自定义类型声明
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    ".next", // 排除构建目录
    "public", // 排除静态资源目录
    "cypress", // 如有测试目录可排除
    "pm2.config.js" // 排除 JS 配置文件
  ]
}
import req from '@/utils/request';
import { CreateParams, UpdateParams, KonwListParams, KonwDetailParams } from '@/types/konwledge/index';

export const create = ({ name, content = '' }: CreateParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/know/create', { name, content });

export const update = ({ id, name, content = '' }: UpdateParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/know/update', { id, name, content });

export const del = (id: number): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/know/del', { id });

export const getList = ({ params: { name }, current, pageSize }: KonwListParams): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/know/list', { params: { name }, current, pageSize });

/**
 * 检索测试
 * @param data
 * @returns
 */

export const getSearchTest = (data: any): Promise<any> => req.post('/ai-manage/v1/know/searchTest', data);
export const uploadFileReq = (formData: FormData): Promise<SERES_RESPONSE> =>
  req.post('/ai-manage/v1/document/uploadFile', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
export const download = (id: number): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/document/download', { id });

export const getDocList = ({
  params: { name, knowId },
  current,
  pageSize,
}: KonwDetailParams): Promise<SERES_RESPONSE> =>
  req.post(`/ai-manage/v1/document/list`, {
    params: { name, knowId },
    current,
    pageSize,
  });

export const analysis = (id: number): Promise<SERES_RESPONSE> => req.post('/ai-manage/v1/document/analysis', { id });

export const deleteDocument = (id: number): Promise<SERES_RESPONSE> => req.post(`/ai-manage/v1/document/del`, { id });

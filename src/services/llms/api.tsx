import req from '@/utils/request';
import {
  LlmsListQuery,
  AddLLMModelData,
  updateLLMModelData
} from '@/types/llms/index';


//查询大模型列表
export const getAiLlmList = ({
  brand,
  title,
  pageNumber,
  pageSize,
}: LlmsListQuery): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.get(`/ai-flow/api/v1/aiLlm/page?brand=${brand}&title=${title}&pageNumber=${pageNumber}&pageSize=${pageSize}`);


//查询模型列表
export const getAiLlmModelList = (): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.get(`/ai-flow/api/v1/aiLlmBrand/list?asTree=true`);

//新增模型
export const save = (data: AddLLMModelData): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiLlm/save`, data);

//编辑模型
export const update = (data: updateLLMModelData): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiLlm/update`, data);

//单个删除模型
export const remove = (id: string): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiLlm/remove`, { id });

//批量删除模型
export const removeBatch = (ids: string[]): Promise<SERES_LLMS_RESPONSE<any>> =>
  req.post(`/ai-flow/api/v1/aiLlm/removeBatch`, { ids });

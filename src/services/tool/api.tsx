

import req from '@/utils/request';
import { ToolListQuery,ToolPluginUpdate, } from "@/types/tool/index";

//查询大模型列表
export const aiPluginList = ({
    name,
    pageNumber,
    pageSize,
}: ToolListQuery): Promise<SERES_LLMS_RESPONSE<any>> =>
    req.get(`/ai-flow/api/v1/aiPlugin/page?name=${name}&pageNumber=${pageNumber}&pageSize=${pageSize}`);

//新增插件
    export const aiPluginList = ({
    name,
    pageNumber,
    pageSize,
}: ToolListQuery): Promise<SERES_LLMS_RESPONSE<any>> =>
    req.get(`/ai-flow/api/v1/aiPlugin/page?name=${name}&pageNumber=${pageNumber}&pageSize=${pageSize}`);

    //编辑插件
    export const aiPluginList = (data:ToolPluginUpdate): Promise<SERES_LLMS_RESPONSE<any>> =>
    req.get(`/ai-flow/api/v1/aiPlugin/plugin/update`,data);

    //删除插件



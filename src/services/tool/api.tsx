

import req from '@/utils/request';
import { ToolListQuery, ToolPluginUpdate, ToolPluginAdd } from "@/types/tool/index";

//查询大模型列表
export const aiPluginList = ({
    name,
    pageNumber,
    pageSize,
}: ToolListQuery): Promise<SERES_LLMS_RESPONSE<any>> =>
    req.get(`/ai-flow/api/v1/aiPlugin/page?name=${name}&pageNumber=${pageNumber}&pageSize=${pageSize}`);

//新增插件
export const save = (data: ToolPluginAdd): Promise<SERES_LLMS_RESPONSE<any>> =>
    req.post(`/ai-flow/api/v1/aiPlugin/plugin/save`, data);

//编辑插件
export const update = (data: ToolPluginUpdate): Promise<SERES_LLMS_RESPONSE<any>> =>
    req.post(`/ai-flow/api/v1/aiPlugin/plugin/update`, data);

//删除插件
export const remove = (id: string): Promise<SERES_LLMS_RESPONSE<any>> =>
    req.post(`/ai-flow/api/v1/aiPlugin/plugin/remove`, { id });


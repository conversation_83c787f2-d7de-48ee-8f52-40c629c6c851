// 定义插件列表查询数据
export interface ToolListQuery {
  name: string;
  pageNumber: number;
  pageSize: number;
}

// 定义插件项数据结构
export interface ToolItem {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  category?: string;
  status?: string;
  createTime?: string;
  updateTime?: string;
}

//定义新增插件数据结构
export interface ToolPluginAdd {
  icon: string; //图标
  name: string; //插件名称
  description: string; //插件描述
  baseUrl: string; //插件 URL
  headers: string; //Headers
  authType: string; //认证方式
  position: string; //参数位置
  tokenKey: string; //tokenKey
  tokenValue: string; //tokenValue
}

//定义编辑插件数据结构
export interface ToolPluginUpdate extends ToolPluginAdd {
  id: string;
}

//定义新增工具数据结构
export interface ToolAdd {
  description: string; //工具描述
  name: string; //工具名称
  pluginId: string; //工具插件ID
}

// 定义工具列表查询数据
export interface PluginToolListQuery {
  pluginId: string;
  name: string;
  pageNumber: number;
  pageSize: number;
}

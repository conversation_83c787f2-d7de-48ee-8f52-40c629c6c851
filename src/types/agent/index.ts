import { applyType } from '../../services/agent/api';
// 定义 CardItem 的数据结构
export interface CardData {
  id: number;
  icon?: string;
  name: string;
  source: string;
  sourceName: string;
  applyType: string;
  applyTypeName: string;
  description?: string;
  createdAt: string;
  createByName: string;
  avatar?: string;
}

export interface DocData {
  id?: number;
  name: string;
  describe?: string;
  loading: boolean;
}

export interface ConfigType {
  type: string;
  config: {
    [key: string]: string;
  };
}
export interface CreateParams {
  name: string;
  description?: string;
  isPublic: boolean;
  config: ConfigType;
  relevanceAgentInfo?: string;
  avatar?: string;
}

export interface UpdateParams extends CreateParams {
  agentId: number;
}

export interface ListParams {
  params: {
    search?: string; // 修改为可选参数
    isMine?: boolean;
  };
  current: number;
  pageSize: number;
}
export interface AgentParams {
  agentId: number;
  type: string;
  config: { serverName: string } | { url: string; apiKeyName: string; apiKeyValue: string };
}
export interface AgentFormData {
  description: string;
  isPublic: boolean;
  id: number;
  knowledgeList?: DocData[];
  name: string;
  relevanceAgentInfo?: string;
  source: string;
  config: ConfigType;
  prePrompt?: string;
}

export interface AgentInfo {}

export interface VariableItem {
  variableName: string;
  variableValue: string;
  desp: string;
}

export interface AgentLogMessage {
  current: number;
  pageSize: number;
  conversationId: string;
}

export interface AgentLogList {
  params: {
    search?: string; // 修改为可选参数
    startTime?: string;
    endTime?: string;
  };
  current: number;
  pageSize: number;
}

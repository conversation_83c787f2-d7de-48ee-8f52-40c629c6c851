:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
 color: rgb(var(--foreground-rgb));
  margin: 0;
  padding: 0;
  background-color: #eeefef;
 
}

/* body {
  display: flow-root;
  min-height: 100vh;
   color: rgb(var(--foreground-rgb));
  line-height: 1.6;
  font-family: 'Roboto', 'Noto Sans SC', 'HarmonyOS Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */

div, span, img, p, main {
  box-sizing: border-box;
}

.home {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  flex-wrap: wrap;
}

.bytemd {
  height: calc(100vh - 260px) !important;
}



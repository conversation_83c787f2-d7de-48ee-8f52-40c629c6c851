'use client';
import { useEffect, useState } from 'react';
import Layout from '@/components/LayoutAgent';
import { message, Typography } from 'antd';
import { useParams, useSearchParams } from 'next/navigation';
import { SettingOutlined } from '@ant-design/icons';
import AgentConfig from './components/configPage';
import Chat from './components/chat';
import { agentDetail, getAgentApi } from '@/services/agent/api';
import { AgentOperateContextProvider } from '@/contexts/agentOperateContext';
import { VariableItem } from '@/types/agent/index';
import OutsideFlow from './components/workflow/outside'
import StyleSheet from './page.module.less';

const { Title } = Typography;

const Agent: React.FC = () => {
  const { appId } = useParams();
  const searchParams = useSearchParams();

  // 获取查询参数
  const source = searchParams.get('source');
  const applyType = searchParams.get('applyType');

  console.log("appId", appId);
  console.log("查询参数 - source:", source, "applyType:", applyType);

  const [detail, setDetail] = useState<any>({});
  const [chatUrl, setChatUrl] = useState<string>('');
  const [variables, setVariables] = useState<VariableItem[]>([]);
  const [prompt, setPrompt] = useState<string>('');

  useEffect(() => {
    getDetail();
  }, []);

  //获取详情
  const getDetail = async () => {
    const res = await agentDetail(Number(appId));
    if (res.success) {
      setDetail(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  //agent 获取agent URL
  const getApi = async () => {
    const res = await getAgentApi(Number(appId));
    if (res.success) {
      setChatUrl(res.resp[0].url);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  useEffect(() => {
    if (detail.relevanceAgentInfo) {
      getApi();
    }
    console.log('detail', detail);
  }, [detail]);

  return (
    <Layout curActive={`/agent/${appId}/operate`} detailData={detail}>
      {detail.source === 'inter' && (
        <Title level={4} className={StyleSheet.title}>
          <SettingOutlined />
          智能体配置
        </Title>
      )}
      {
        applyType === 'chat' ? <div className={detail.source === 'inter' ? StyleSheet.container : StyleSheet['container-external']}>
          {detail.source === 'inter' && (
            <AgentOperateContextProvider
              contextValue={{
                initValues: detail,
                getDetail,
                getApi,
              }}
            >
              <AgentConfig setVariables={setVariables} variables={variables} prompt={prompt} setPrompt={setPrompt} />
            </AgentOperateContextProvider>
          )}
          <Chat chatUrl={chatUrl} variables={variables} prompt={prompt} />
        </div> : <>{detail.source === 'inter' ? <OutsideFlow /> : <div>内部</div>}</>
      }

    </Layout>
  );
};

export default Agent;

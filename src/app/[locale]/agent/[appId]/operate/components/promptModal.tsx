'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, Typography, message, Spin } from 'antd';
import { RobotOutlined } from '@ant-design/icons';
import { optimizePrompt } from '@/services/agent/api';
import styles from './promptModal.module.less';

const { TextArea } = Input;
const { Paragraph } = Typography;

interface PromptModalProps {
  open: boolean;
  onCancel: () => void;
  prompt: string;
  onSave: (prompt: string) => void;
}

const PromptModal: React.FC<PromptModalProps> = ({ open, onCancel, prompt, onSave }) => {
  const [form] = Form.useForm();
  const [content, setContent] = useState(prompt);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [loading, setLoading] = useState(false);
  const textAreaRef = React.useRef<any>(null);

  useEffect(() => {
    if (open) {
      setLoading(true);
      optimizePrompt(prompt)
        .then((res) => {
          if (res.success) {
            setContent(res.resp[0].bestPrompt);
            form.setFieldsValue({
              content: res.resp[0].bestPrompt,
            });
          } else {
            message.error(res.msg || '优化失败');
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [open, prompt, form]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
    setCursorPosition(e.target.selectionStart);
  };

  const handleCursorPositionChange = (
    e: React.MouseEvent<HTMLTextAreaElement> | React.KeyboardEvent<HTMLTextAreaElement>,
  ) => {
    setCursorPosition(e.currentTarget.selectionStart);
  };

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      onSave(values.content);
    });
  };

  const handleRegenerate = () => {
    // 重新生成提示词的逻辑
    setLoading(true);
    optimizePrompt(content)
      .then((res) => {
        if (res.success) {
          setContent(res.resp[0].bestPrompt);
          form.setFieldsValue({
            content: res.resp[0].bestPrompt,
          });
        } else {
          message.error(res.msg || '优化失败');
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Modal title="提示词" open={open} onCancel={onCancel} width={800} footer={null} className={styles.promptModal}>
      <div className={styles.promptDescription}>
        <Paragraph>使用配置的模型来优化提示词，以获得更高的质量和更好的结构。请写出清晰详细的说明。</Paragraph>
      </div>

      <Form form={form} layout="vertical">
        <Form.Item name="content" rules={[{ required: true, message: '请输入提示词内容' }]}>
          <Spin spinning={loading} tip="优化中...">
            <TextArea
              ref={textAreaRef}
              value={content}
              placeholder="请输入提示词内容"
              autoSize={{ minRows: 10, maxRows: 20 }}
              onChange={handleContentChange}
              onClick={handleCursorPositionChange}
              onKeyUp={handleCursorPositionChange}
              className={styles.promptTextarea}
              disabled={loading}
            />
          </Spin>
        </Form.Item>
      </Form>

      <div style={{ display: 'flex', justifyContent: 'space-between', gap: '10px' }}>
        <div>
          <Button
            type="primary"
            icon={<RobotOutlined />}
            className={styles.regenerateButton}
            onClick={handleRegenerate}
            ghost
            loading={loading}
            disabled={loading}
          >
            重新生成
          </Button>
        </div>

        <div style={{ display: 'flex', gap: '10px' }}>
          <Button key="cancel" onClick={onCancel} disabled={loading}>
            取消
          </Button>
          <Button key="submit" type="primary" onClick={handleSubmit} disabled={loading}>
            使用
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default PromptModal;

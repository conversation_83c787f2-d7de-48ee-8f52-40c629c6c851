.configCard {
    margin-bottom: 15px !important;
    border-radius: 10px;
    border: 1px solid #f0f0f0;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:last-child {
        margin-bottom: 0 !important;
    }

    :global {
        .ant-card-head {
            border-bottom: 1px solid #f0f0f0;
            padding: 0 20px;
            height: 60px;
            background-color: #f0f7ff;

            .ant-card-head-title {
                font-size: 16px;
                font-weight: 600;
                color: #1677ff;
            }
        }

        .ant-card-body {
            padding: 24px;
        }
    }
}

// 提示词部分样式
.promptSection {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.promptContent {
    background: #f9f9f9;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 12px;
    font-family: 'Courier New', monospace;
    line-height: 1.6;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

// 变量部分样式
.variableSection {
    padding: 8px 0;
}

.variableHeader {
    margin-bottom: 16px;

    span {
        color: #8c8c8c;
        font-size: 14px;
    }
}

.variableTagList {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 16px;
}

.variableTag {
    display: inline-block;
    padding: 6px 16px;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
        border-color: #1677ff;
        color: #1677ff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
}

.emptyVariables {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 32px 0;
    color: #8c8c8c;
    text-align: center;
    height: 120px;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.03);

    &:hover {
        background: #f5f5f5;
        border-color: #bfbfbf;
    }
}

// 知识库部分样式
.knowledgeHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    small {
        margin-left: 8px;
        font-size: 12px;
    }
}

.knowledgeList {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.knowledge {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 12px 16px;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    span {
        display: flex;
        align-items: center;
        gap: 8px;

        i {
            color: #1677ff;
            font-size: 16px;
        }
    }
}

// 空状态样式
.emptyKnowledge {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 120px;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
    margin-top: 16px;
    color: #999;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.03);

    &:hover {
        background: #f5f5f5;
        border-color: #bfbfbf;
    }
}
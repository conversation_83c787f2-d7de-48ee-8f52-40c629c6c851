'use client';
import React, { useEffect } from 'react';
import { Modal, Form, Input, Button, Typography, Space } from 'antd';
import { PlusOutlined, DeleteOutlined, KeyOutlined } from '@ant-design/icons';
import { VariableItem } from '@/types/agent/index';
import styles from './variableModal.module.less';

const { Text } = Typography;

interface VariableModalProps {
  open: boolean;
  onCancel: () => void;
  onSave: (variables: VariableItem[]) => void;
  initialVariables?: VariableItem[];
  editingVariable?: VariableItem;
  mode: 'add' | 'edit';
}

const VariableModal: React.FC<VariableModalProps> = ({ open, onCancel, onSave, initialVariables = [], mode }) => {
  const [form] = Form.useForm();

  // 重置状态
  useEffect(() => {
    if (open) {
      // 如果有初始变量，设置表单值
      if (initialVariables.length > 0) {
        form.setFieldsValue({ variables: initialVariables });
      } else {
        // 否则初始化一个空的变量列表
        form.setFieldsValue({ variables: [{ variableName: '', desp: '', variableValue: '' }] });
      }
    }
  }, [open, initialVariables, form]);

  // 保存所有变量
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      // 过滤掉空的变量名
      const validVariables = values.variables.filter((v: VariableItem) => v.variableName.trim() !== '');
      onSave(validVariables);
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
  };

  // 当模态框关闭时，重置表单
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={mode === 'add' ? '添加变量' : '编辑变量'}
      open={open}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSave}>
          保存
        </Button>,
      ]}
      className={styles.variableModal}
      destroyOnClose
    >
      <Form form={form} layout="vertical" name="variableForm">
        <div className={styles.variableTable}>
          <div className={styles.tableHeader}>
            <div className={styles.nameColumn}>
              名称<span className={styles.required}>*</span>
            </div>
            <div className={styles.descColumn}>
              描述<span className={styles.required}>*</span>
            </div>
            <div className={styles.valueColumn}>
              变量值<span className={styles.required}>*</span>
            </div>
            <div className={styles.actionColumn}>操作</div>
          </div>

          <Form.List name="variables">
            {(fields, { add, remove }) => (
              <>
                <div className={styles.tableBody}>
                  {fields.length > 0 ? (
                    fields.map((field) => (
                      <div key={field.key} className={styles.tableRow}>
                        <div className={styles.nameColumn}>
                          <Form.Item
                            {...field}
                            name={[field.name, 'variableName']}
                            rules={[{ required: true, message: '请输入变量名称' }]}
                            noStyle
                          >
                            <Input placeholder="请输入（必填）" />
                          </Form.Item>
                        </div>
                        <div className={styles.descColumn}>
                          <Form.Item
                            {...field}
                            name={[field.name, 'desp']}
                            rules={[{ required: true, message: '请输入描述' }]}
                            noStyle
                          >
                            <Input placeholder="请输入（必填）" />
                          </Form.Item>
                        </div>
                        <div className={styles.valueColumn}>
                          <Form.Item
                            {...field}
                            name={[field.name, 'variableValue']}
                            rules={[{ required: true, message: '请输入变量值' }]}
                            noStyle
                          >
                            <Input placeholder="请输入（必填）" />
                          </Form.Item>
                        </div>
                        <div className={styles.actionColumn}>
                          <Button
                            type="text"
                            icon={<DeleteOutlined />}
                            onClick={() => remove(field.name)}
                            className={styles.deleteButton}
                          />
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className={styles.emptyVariables}>
                      <Space direction="vertical" align="center">
                        <KeyOutlined style={{ fontSize: '24px', opacity: 0.5 }} />
                        <Text type="secondary">暂无变量，点击&apos;添加变量&apos;按钮创建</Text>
                      </Space>
                    </div>
                  )}
                </div>

                <Button
                  type="link"
                  onClick={() => add({ variableName: '', desp: '', variableValue: '' })}
                  icon={<PlusOutlined />}
                  className={styles.addButton}
                >
                  添加变量
                </Button>
              </>
            )}
          </Form.List>
        </div>
      </Form>
    </Modal>
  );
};

export default VariableModal;

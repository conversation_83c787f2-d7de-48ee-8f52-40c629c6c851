'use client';
import React, { useRef, useState } from 'react';
import { Tinyflow, TinyflowHandle } from '@tinyflow-ai/react';
import '@tinyflow-ai/react/dist/index.css';

const outsideFlow = () => {
  const tinyflowRef = useRef<TinyflowHandle>(null);

  const handleGetData = () => {
    if (tinyflowRef.current) {
      const data = tinyflowRef.current.getData();
      console.log('Flow Data:', data);
    }
  };

  return (
    <div>
      <Tinyflow
        ref={tinyflowRef}
        data={{
          nodes: [],
          edges: [],
        }}
        style={{ border: '1px solid #ccc' }}
        className="custom-class"
      />
      <button onClick={handleGetData}>获取流程数据</button>
    </div>
  );
};

export default outsideFlow;

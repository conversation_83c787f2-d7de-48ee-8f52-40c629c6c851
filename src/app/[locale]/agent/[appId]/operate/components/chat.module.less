.chatContainer {
  width: 100%;
  max-width: 900px;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.chatHeader {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1677ff;
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.chatContent {
  height: 500px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  :global {
    .ant-pro-chat {
      border-radius: 8px;
    }

    .ant-pro-chat-container {
      border-radius: 8px;
    }

    .ant-pro-chat-input-container {
      border-top: 1px solid #f0f0f0;
      padding: 12px;
      // background-color: #f9f9f9;background-colorbackground-color
    }

    .ant-pro-chat-input {
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:focus,
      &:hover {
        border-color: #1677ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }

    // .ant-pro-chat-message-content {
    //   border-radius: 8px;border-radiusborder-radius
    //   padding: 12px 16px;paddingpadding
    //   box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);box-shadowbox-shadow
    // }

    // .ant-pro-chat-message-user .ant-pro-chat-message-content {
    //   background-color: #e6f4ff;background-colorbackground-color
    // }

    // .ant-pro-chat-message-assistant .ant-pro-chat-message-content {
    //   background-color: #f5f5f5;background-colorbackground-color
    // }
  }
}

.uploadSection {
  margin-top: 16px;

  :global {
    .ant-upload-list-item {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .ant-upload-select {
      border-radius: 8px !important;
      border: 1px dashed #d9d9d9 !important;

      &:hover {
        border-color: #1677ff !important;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.05) !important;
      }
    }
  }
}
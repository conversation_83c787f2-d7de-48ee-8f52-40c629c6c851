'use client';
import { memo, useEffect, useState } from 'react';
import { FileTextOutlined } from '@ant-design/icons';
import { Modal, List, Skeleton, Button, Space, message } from 'antd';
import { DocData } from '@/types/agent/index';
import { getList } from '@/services/konwledge/api';
import StyleSheet from './docModal.module.less';
let current = 1; //初始化页码
const pageSize = 10; //每页显示条数

const DocModal: React.FC<{
  isDocModalOpen: boolean;
  setIsDocModalOpen: (open: boolean) => void;
  addKnow: (selected: DocData[]) => void;
  selectedKnow: DocData[];
}> = ({ isDocModalOpen, setIsDocModalOpen, addKnow, selectedKnow }) => {
  const [selected, setSelected] = useState<DocData[]>([]);
  const [initLoading, setInitLoading] = useState(true);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<DocData[]>([]);
  const [list, setList] = useState<DocData[]>([]);
  const [hasMore, setHasMore] = useState(true);

  const handleOk = async () => {
    addKnow(selected);
    handleCancel();
  };
  const handleCancel = () => {
    setIsDocModalOpen(false);
  };

  //点击知识库
  const toggleSelect = (dataSet: DocData) => {
    const isSelected = selected.some((item) => item.id === dataSet.id);
    if (isSelected) {
      setSelected(selected.filter((item) => item.id !== dataSet.id));
    } else {
      setSelected([...selected, dataSet]);
    }
  };

  //加载更多列表逻辑
  useEffect(() => {
    getList({
      params: { name: '' },
      current,
      pageSize,
    }).then(({ success, resp, msg }) => {
      if (success) {
        setData(resp[0].list);
        setInitLoading(false);
        setList(resp[0].list);
        if (resp[0].pagination.total <= pageSize) {
          setHasMore(false);
        }
      } else {
        message.error(msg || '查询失败');
      }
    });
  }, []);

  useEffect(() => {
    setSelected(selectedKnow);
  }, [selectedKnow]);

  useEffect(() => {
    isDocModalOpen && setSelected(selectedKnow);
  }, [isDocModalOpen]);

  const onLoadMore = () => {
    current++;
    setLoading(true);
    setList(
      data.concat(
        Array.from({ length: 10 }).map(() => ({
          loading: true,
          name: '',
        })),
      ),
    );
    getList({
      params: { name: '' },
      current,
      pageSize,
    }).then(({ success, resp }) => {
      if (success) {
        const newData = data.concat(resp[0].list);
        setData(newData);
        setList(newData);
        setLoading(false);
        if (resp[0].pagination.total <= newData.length) {
          setHasMore(false);
        }
        window.dispatchEvent(new Event('resize'));
      }
    });
  };

  const loadMore =
    !initLoading && !loading && hasMore ? (
      <div
        style={{
          textAlign: 'center',
          marginTop: 12,
          height: 32,
          lineHeight: '32px',
        }}
      >
        <Button onClick={onLoadMore}>加载更多</Button>
      </div>
    ) : null;
  return (
    <Modal
      title="选择引用知识库"
      open={isDocModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      footer={(_, { OkBtn, CancelBtn }) => (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span style={{ fontWeight: 'bold' }}>{!!selected.length && `${selected.length}个知识库被选中`}</span>
          <Space>
            <CancelBtn />
            <OkBtn />
          </Space>
        </div>
      )}
    >
      <List
        className={`"demo-loadmore-list ${StyleSheet.docList}`}
        loading={initLoading}
        itemLayout="horizontal"
        loadMore={loadMore}
        dataSource={list}
        renderItem={(item) => (
          <List.Item
            className={selected.some((i) => i.id === item.id) ? 'selected' : ''}
            onClick={() => {
              toggleSelect(item);
            }}
          >
            <Skeleton avatar title={false} loading={item.loading} active>
              <List.Item.Meta avatar={<FileTextOutlined />} title={<span>{item.name}</span>} />
            </Skeleton>
          </List.Item>
        )}
      />
    </Modal>
  );
};
export default memo(DocModal);

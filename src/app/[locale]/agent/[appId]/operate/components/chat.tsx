'use client';
import { memo, useState, useEffect } from 'react';
import { RobotOutlined } from '@ant-design/icons';
import { ProChat } from '@ant-design/pro-chat';
import { message, Typography } from 'antd';
import { useTheme } from 'antd-style';
import Cookies from 'js-cookie';
import axios, { AxiosError } from 'axios';
import { SSEResponse } from '@/utils/sseResponse';
import { VariableItem } from '@/types/agent/index';
import styles from './chat.module.less';

const { Title } = Typography;

interface IProps {
  chatUrl: string;
  variables: VariableItem[];
  prompt: string;
}

const Chat: React.FC<IProps> = ({ chatUrl, variables, prompt }) => {
  const theme = useTheme();
  const [value, setValue] = useState<string>('');
  const [conversationId, setConversationId] = useState<string>('');
  const [messageId, setMessageId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [chatInputs, setChatInputs] = useState({});

  useEffect(() => {
    const obj = variables.reduce((acc, item) => {
      acc[item.variableName] = item.variableValue;
      return acc;
    }, {});
    setChatInputs(obj);
  }, [variables]);

  return (
    <div className={styles.chatContainer}>
      <div className={styles.chatHeader}>
        <Title level={4}>
          <RobotOutlined />
          <span>智能对话</span>
        </Title>
      </div>

      <div className={styles.chatContent} style={{ background: theme.colorBgLayout, minHeight: '90%' }}>
        <ProChat
          locale="zh-CN"
          style={{ height: '700px' }}
          userMeta={{
            avatar: '/images/logo_bg.jpeg',
          }}
          assistantMeta={{
            avatar: '/images/robot.png',
            backgroundColor: '#1677ff',
          }}
          inputAreaProps={{
            value: value,
            onChange: (e) => {
              setValue(e as string);
            },
            placeholder: '请输入您的问题...',
          }}
          request={async (messages: any) => {
            try {
              setLoading(true);
              const responseData = await axios.post(
                `${chatUrl}/v1/chat-messages`,
                {
                  inputs: chatInputs,
                  query: messages.slice(-1)[0].content,
                  conversation_id: conversationId,
                  user: messages.slice(-1)[0].role,
                  response_mode: 'streaming',
                  parent_message_id: messageId,
                  model_config: {
                    pre_prompt: prompt,
                  },
                },
                { headers: { Authorization: Cookies.get('access_token') } },
              );

              const data = responseData.data.split('\n\n').filter((item: string) => item !== '');

              const mockResponse = new SSEResponse(data);
              const response = mockResponse.getResponse();
              // 获取 reader
              const reader = response?.body?.getReader();

              const decoder = new TextDecoder('utf-8');
              const encoder = new TextEncoder();
              const readableStream = new ReadableStream({
                async start(controller) {
                  function push() {
                    reader &&
                      reader
                        .read()
                        .then(({ done, value }) => {
                          if (done) {
                            controller.close();
                            setLoading(false);
                            return;
                          }
                          const chunk = decoder.decode(value, { stream: true });
                          const message = chunk.replace('data: ', '');
                          const parsed = JSON.parse(message);
                          if (parsed.event === 'agent_message') {
                            controller.enqueue(encoder.encode(parsed.answer));
                          }
                          if (parsed.event === 'message_end') {
                            setConversationId(parsed.conversation_id);
                            setMessageId(parsed.message_id);
                          }
                          push();
                        })
                        .catch((err) => {
                          console.error('读取流中的数据时发生错误', err);
                          controller.error(err);
                          setLoading(false);
                        });
                  }
                  push();
                },
              });

              return new Response(readableStream);
            } catch (e) {
              setLoading(false);
              if (axios.isAxiosError(e)) {
                // 类型守卫
                const error = e as AxiosError<{ msg: string }>;
                if (error.response && error.response.data && error.response.data.msg) {
                  message.error(error.response.data.msg);
                } else {
                  message.error('未知错误，请稍后重试！');
                }
              } else {
                message.error('系统异常，请联系管理员！');
              }
            }
          }}
        />
      </div>

      {/* <div className={styles.uploadSection}>
        <Form form={form} layout="vertical">
          <Form.Item label="上传图片进行分析" name="image">
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={(file) => {
                if (file.type === 'image/png') {
                  return true;
                } else {
                  message.error('请上传png格式的图片');
                  return Upload.LIST_IGNORE;
                }
              }}
              action="https://run.mocky.io/v3/435e224c-44fb-4773-9faf-380c5e6a2188"
            >
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            </Upload>
          </Form.Item>
        </Form>
      </div> */}
    </div>
  );
};
export default memo(Chat);

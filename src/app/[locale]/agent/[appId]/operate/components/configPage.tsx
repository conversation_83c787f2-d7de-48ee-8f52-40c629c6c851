'use client';
import { useEffect, useState, memo } from 'react';
import { useParams } from 'next/navigation';
import { Button, Form, Space, message, Card, Input, Typography, Divider, Tooltip } from 'antd';
import {
  PlusOutlined,
  FileTextOutlined,
  DeleteOutlined,
  EditOutlined,
  MessageOutlined,
  KeyOutlined,
  BookOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import DocModal from './docModal';
import PromptModal from './promptModal';
import VariableModal from './variableModal';
import { saveKnow, delKnowledge, batchAddVariable, getVariables, savePrompt } from '@/services/agent/api';
import { DocData } from '@/types/agent/index';
import { useAgentOperateContext } from '@/contexts/agentOperateContext';
import { VariableItem } from '@/types/agent/index';
import styles from './configPage.module.less';

const { TextArea } = Input;
const { Text } = Typography;

interface AgentFormProps {
  variables: VariableItem[];
  setVariables: (variables: VariableItem[]) => void;
  prompt: string;
  setPrompt: (prompt: string) => void;
}

const AgentForm: React.FC<AgentFormProps> = ({ variables, setVariables, prompt, setPrompt }) => {
  const { initValues } = useAgentOperateContext() ?? {};
  const [form] = Form.useForm();
  const { appId } = useParams();
  const [isDocModalOpen, setIsDocModalOpen] = useState(false);
  const [isPromptModalOpen, setIsPromptModalOpen] = useState(false);
  const [isVariableModalOpen, setIsVariableModalOpen] = useState(false);
  const [selectedKnow, setSelectedKnow] = useState<DocData[]>([]);
  // const [variables, setVariables] = useState<VariableItem[]>([]);
  // const [prompt, setPrompt] = useState<string>('');
  const [currentPrompt, setCurrentPrompt] = useState<string>('');
  const [mode, setModal] = useState<'add' | 'edit'>('add');

  // 添加知识库
  const addKnow = async (selected: DocData[]) => {
    const { success, resp, msg } = await saveKnow({
      agentId: Number(appId),
      knowledgeIdList: selected.map((item) => item.id).filter((id): id is number => typeof id === 'number'),
    });
    if (success) {
      message.success('添加成功');
      setSelectedKnow(resp[0].knowledgeList);
    } else {
      message.error(msg || '添加失败');
    }
  };

  // 删除知识库
  const deleteKnow = async (knowledgeId: number) => {
    const { success, resp, msg } = await delKnowledge({
      agentId: Number(appId),
      knowledgeId,
    });
    if (success) {
      message.success('删除成功');
      setSelectedKnow(resp[0].knowledgeList);
    } else {
      message.error(msg || '删除失败');
    }
  };

  // 添加变量
  const addVariables = async (newVariables: VariableItem[]) => {
    const { success, msg } = await batchAddVariable({ agentId: Number(appId), variableList: newVariables });
    if (success) {
      message.success('添加成功');
      setVariables(newVariables);
      setIsVariableModalOpen(false);
    } else {
      message.error(msg || '添加失败');
    }
  };

  // 打开提示词优化弹窗
  const openPromptModal = (prompt: string) => {
    setIsPromptModalOpen(true);
    setCurrentPrompt(prompt);
  };

  // 更新提示词
  const updatePrompt = async (updatedPrompt: string) => {
    const { success, msg } = await savePrompt({ agentId: Number(appId), prePrompt: updatedPrompt });
    if (success) {
      setPrompt(updatedPrompt);
      setIsPromptModalOpen(false);
    } else {
      message.error(msg || '更新失败');
    }
  };

  useEffect(() => {
    getVariables(Number(appId)).then(({ success, resp, msg }) => {
      if (success) {
        setVariables(resp);
      } else {
        message.error(msg || '查询失败');
      }
    });
  }, [appId]);

  useEffect(() => {
    if (initValues) {
      form.setFieldsValue(initValues);
      setSelectedKnow(initValues.knowledgeList || []);
      setPrompt(initValues.prePrompt || '');
      // 这里可以添加初始化变量和提示词的逻辑
    }
  }, [initValues]);

  return (
    <div>
      {/* 提示词部分 */}
      <Card
        className={styles.configCard}
        title={
          <Space>
            <MessageOutlined />
            <span>提示词</span>
          </Space>
        }
        extra={
          <Button type="primary" icon={<EditOutlined />} onClick={() => openPromptModal(prompt)}>
            优化
          </Button>
        }
      >
        <div className={styles.promptSection}>
          <TextArea
            value={prompt}
            placeholder='在这里写提示词，输入"{"插入变量'
            rows={8}
            className={styles.promptContent}
            onChange={(e) => setPrompt(e.target.value)}
            showCount={true}
          />
        </div>
      </Card>

      {/* 变量部分 */}
      <Card
        className={styles.configCard}
        title={
          <Space>
            <KeyOutlined />
            <span>变量</span>
          </Space>
        }
        extra={
          <Button
            type="primary"
            onClick={() => {
              setModal('add');
              setIsVariableModalOpen(true);
            }}
          >
            <PlusOutlined />
            添加变量
          </Button>
        }
      >
        <div className={styles.variableSection}>
          <div className={styles.variableHeader}>
            <Text type="secondary">可以添加变量，使智能体更加个性化！</Text>
          </div>

          {variables.length > 0 ? (
            <div className={styles.variableTagList}>
              {variables.map((variable) => (
                <div
                  key={variable.variableName}
                  className={styles.variableTag}
                  onClick={() => {
                    setIsVariableModalOpen(true);
                    setModal('edit');
                  }}
                >
                  {variable.variableName}
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.emptyVariables}>
              <Space direction="vertical" align="center">
                <KeyOutlined style={{ fontSize: '24px', opacity: 0.5 }} />
                <Text type="secondary">暂无变量，点击&apos;添加&apos;按钮创建</Text>
              </Space>
            </div>
          )}
        </div>
      </Card>

      {/* 知识库部分 */}
      <Card
        className={styles.configCard}
        title={
          <Space>
            <BookOutlined />
            <span>知识库</span>
          </Space>
        }
      >
        <div className={styles.knowledgeHeader}>
          <Space>
            <Text type="secondary">导入一个或多个知识库，增强AI回答能力</Text>
            <Tooltip title="知识库可以帮助AI更准确地回答特定领域的问题">
              <InfoCircleOutlined style={{ color: '#1677ff' }} />
            </Tooltip>
          </Space>
          <Button type="primary" onClick={() => setIsDocModalOpen(true)}>
            <PlusOutlined />
            添加知识库
          </Button>
        </div>

        {!!selectedKnow?.length ? (
          <div className={styles.knowledgeList}>
            {selectedKnow.map((item) => (
              <div className={styles.knowledge} key={item.id}>
                <Space>
                  <FileTextOutlined />
                  <div>{item.name}</div>
                </Space>
                <Button type="text" danger icon={<DeleteOutlined />} onClick={() => deleteKnow(item.id!)} />
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.emptyKnowledge}>
            <Space direction="vertical" align="center">
              <BookOutlined style={{ fontSize: '24px', opacity: 0.5 }} />
              <Text type="secondary">暂无知识库，点击&apos;添加知识库&apos;按钮导入</Text>
            </Space>
          </div>
        )}
      </Card>

      {/* 知识库弹窗 */}
      <DocModal
        isDocModalOpen={isDocModalOpen}
        setIsDocModalOpen={setIsDocModalOpen}
        addKnow={addKnow}
        selectedKnow={selectedKnow}
      />

      {/* 提示词弹窗 */}
      <PromptModal
        open={isPromptModalOpen}
        onCancel={() => setIsPromptModalOpen(false)}
        prompt={currentPrompt}
        onSave={updatePrompt}
      />

      {/* 变量弹窗 */}
      <VariableModal
        open={isVariableModalOpen}
        onCancel={() => {
          setIsVariableModalOpen(false);
        }}
        onSave={addVariables}
        mode={mode}
        initialVariables={variables}
      />
    </div>
  );
};

export default memo(AgentForm);

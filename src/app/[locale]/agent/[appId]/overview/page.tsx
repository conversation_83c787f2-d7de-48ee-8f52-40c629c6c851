'use client';
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Select, message } from 'antd';
import { useParams } from 'next/navigation';
import Layout from '@/components/LayoutAgent';
import StatisticsChart from './components/StatisticsChart';
// import useStore from '@/store/useStore';
import { allMsg, avgResBody, avgResTime, timeoutReqCount, errorReqCount } from '@/services/agent/overViewApi';
import { agentDetail } from '@/services/agent/api';
import styles from './page.module.less';

const { Option } = Select;
interface DataPoint {
  xaxis: string[];
  yaxis: string[];
}

const TIME_RANGE = [
  { label: '今天', value: '1' },
  { label: '过去7天', value: '2' },
  { label: '过去2周', value: '3' },
  { label: '本月至今', value: '4' },
  { label: '过去1个月', value: '5' },
  { label: '过去3个月', value: '6' },
  { label: '过去12个月', value: '7' },
  { label: '所有时间', value: '8' },
];

const OverViewPage: React.FC = () => {
  const agentId = useParams().appId;
  // const { userInfo } = useStore();
  const [timeRange, setTimeRange] = useState<string>('2');
  const [allMsgData, setAllMsgData] = useState<DataPoint>({ xaxis: [], yaxis: [] });
  const [avgResBodyData, setAvgResBodyData] = useState<DataPoint>({ xaxis: [], yaxis: [] });
  const [avgResTimeData, setAvgResTimeData] = useState<DataPoint>({ xaxis: [], yaxis: [] });
  const [timeoutReqCountData, setTimeoutReqCountData] = useState<DataPoint>({ xaxis: [], yaxis: [] });
  const [errorReqCountData, setErrorReqCountData] = useState<DataPoint>({ xaxis: [], yaxis: [] });
  const [period, setPeriod] = useState<string>('过去7天');
  const [detail, setDetail] = useState<any>({});

  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
    setPeriod(TIME_RANGE.filter((item) => item.value === value)[0].label);
  };

  //全部消息数
  const getAllMsg = async () => {
    const res = await allMsg({ id: agentId as string, timeType: timeRange });
    if (res.success) {
      setAllMsgData(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  //数据吞吐量（响应体字符平均数）
  const getAvgResBody = async () => {
    const res = await avgResBody({ id: agentId as string, timeType: timeRange });
    if (res.success) {
      setAvgResBodyData(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  //平均响应时间
  const getAvgResTime = async () => {
    const res = await avgResTime({ id: agentId as string, timeType: timeRange });
    if (res.success) {
      setAvgResTimeData(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  //超时请求
  const getTimeoutReqCount = async () => {
    const res = await timeoutReqCount({ id: agentId as string, timeType: timeRange });
    if (res.success) {
      setTimeoutReqCountData(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  //错误数
  const getErrorReqCount = async () => {
    const res = await errorReqCount({ id: agentId as string, timeType: timeRange });
    if (res.success) {
      setErrorReqCountData(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  useEffect(() => {
    getAllMsg();
    getAvgResBody();
    getAvgResTime();
    getTimeoutReqCount();
    getErrorReqCount();
  }, [agentId, timeRange]);

  //获取智能体详情
  const getDetail = async () => {
    const res = await agentDetail(Number(agentId));
    if (res.success) {
      setDetail(res.resp[0]);
    } else {
      message.error(res.msg || '查询失败');
    }
  };

  useEffect(() => {
    getDetail();
  }, []);

  return (
    <Layout curActive={`/agent/${agentId}/overview`} detailData={detail}>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.title}>监测分析</div>
          <Select value={timeRange} onChange={handleTimeRangeChange} className={styles.customSelect}>
            {TIME_RANGE.map((item) => (
              <Option key={item.value} value={item.value}>
                {item.label}
              </Option>
            ))}
          </Select>
        </div>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <StatisticsChart
              title="全部消息数"
              data={allMsgData}
              period={period}
              tooltip="以自然日为单位，统计 agent 请求总数"
              color="rgba(123,116,255,1)" // Purple
            />
          </Col>
          <Col xs={24} sm={12}>
            <StatisticsChart
              title="数据吞吐量"
              data={avgResBodyData}
              period={period}
              tooltip="响应体字符平均数"
              color="#52c41a" // Green
            />
          </Col>
          <Col xs={24} sm={12}>
            <StatisticsChart
              title="平均响应时间(毫秒)"
              data={avgResTimeData}
              period={period}
              tooltip="API从接收请求到返回响应所耗时间的平均值，反映服务端的处理效率"
              color="#fa8c16" // Orange
            />
          </Col>
          <Col xs={24} sm={12}>
            <StatisticsChart
              title="超时请求"
              data={timeoutReqCountData}
              period={period}
              tooltip="因超时失败的请求数统计"
              color="#1890ff" // Blue
            />
          </Col>
          <Col xs={24} sm={12}>
            <StatisticsChart
              title="错误数"
              data={errorReqCountData}
              period={period}
              tooltip="按HTTP状态码（404/500）错误码统计的错误数"
              color="#ff4d4f" // red
            />
          </Col>
        </Row>
      </div>
    </Layout>
  );
};

export default OverViewPage;

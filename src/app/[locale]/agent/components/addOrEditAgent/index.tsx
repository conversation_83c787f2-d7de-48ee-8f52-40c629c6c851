'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, message, Radio, Space, Flex, Avatar } from 'antd';
import type { FormInstance } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { createAgent, updateAgent, getBaseinfo } from '@/services/agent/api';
import { UpdateParams, AgentFormData, ConfigType, CreateParams } from '@/types/agent/index';
import ConfigModal from './configModal';
import cloneDeep from 'lodash-es/cloneDeep';

const { TextArea } = Input;

interface ParamsType {
  id?: number;
  name: string;
  description: string;
  isPublic: boolean;
  config: ConfigType;
}

interface IProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  initialValues?: Partial<ParamsType>;
  mode?: 'add' | 'edit';
}

const AddOrEditAgent: React.FC<IProps> = ({ open, onCancel, onSuccess, initialValues, mode = 'add' }) => {
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const [config, setConfig] = useState<ConfigType>();
  const [baseInfo, setBaseInfo] = useState<AgentFormData>();
  const [avatarNumber, setAvatarNumber] = useState(1);

  const handleSubmit = async () => {
    try {
      form.validateFields().then(async (values) => {
        setSubmitLoading(true);
        delete values.relevanceAgentInfo;
        if (mode === 'add') {
          createHandle({ ...values, avatar: avatarNumber, config });
        } else {
          // 编辑模式
          updateHandle({ agentId: initialValues?.id, ...values, avatar: avatarNumber, config });
        }
      });
    } catch (error) {
      message.error('保存失败');
    }
  };

  const createHandle = (data: CreateParams) => {
    createAgent(data).then((res) => {
      if (res.success) {
        message.success('创建成功');
        onSuccess();
      } else {
        message.error(res.msg || '创建失败');
      }
      setSubmitLoading(false);
    });
  };

  const updateHandle = (data: UpdateParams) => {
    updateAgent(data).then((res) => {
      if (res.success) {
        message.success('更新成功');
        onSuccess();
      } else {
        message.error(res.msg || '更新失败');
      }
      setSubmitLoading(false);
    });
  };

  //查询agent基本信息
  const getAgentInfo = async (id: number) => {
    const { success, resp, msg } = await getBaseinfo(id);
    if (success) {
      form.setFieldsValue(resp[0]);
      setAvatarNumber(Number(resp[0].avatar ?? 1));
      setBaseInfo(resp[0]);
    } else {
      message.error(msg || '查询失败');
    }
  };

  // 当模态框打开时，设置初始值
  useEffect(() => {
    if (open && initialValues) {
      mode === 'edit' && getAgentInfo(initialValues.id!);
    }
    if (!open) {
      form.resetFields();
    }
  }, [open, initialValues]);

  // 当模态框关闭时，重置表单
  const handleCancel = () => {
    onCancel();
  };

  const openConfigModal = () => {
    setIsConfigModalOpen(true);
    if (!!!form.getFieldValue('relevanceAgentInfo')) {
      setBaseInfo(undefined);
    }
  };

  const onConfigModalCancel = () => {
    setIsConfigModalOpen(false);
  };

  //保存智能体配置
  const configSubmit = (formRef: FormInstance) => {
    try {
      formRef.validateFields().then(async (values) => {
        const values_alias = cloneDeep(values);
        if (values_alias.source === 'inter') {
          form.setFieldValue(
            'relevanceAgentInfo',
            `${Object.keys(values_alias.config)}:${Object.values(values_alias.config)}`,
          );
        } else {
          form.setFieldValue('relevanceAgentInfo', `URL:${values_alias.config.url}`);
        }

        if (values_alias.source === 'dify' && !values_alias.config.Authorization.startsWith('Bearer ')) {
          //特殊处理dify,Authorization加上前缀Bearer
          values_alias.config.Authorization = `Bearer ${values_alias.config.Authorization}`;
        }
        setIsConfigModalOpen(false);
        setConfig(values_alias);
        setBaseInfo(values); //编辑配置回显
      });
    } catch (error) {
      message.error('保存失败');
    }
  };

  //换一个头像
  const changeAvatar = () => {
    const NUM = 3; // /images/agentAvatar里面的图片总数
    let newRandomNumber;
    do {
      newRandomNumber = Math.floor(Math.random() * NUM) + 1;
    } while (newRandomNumber === avatarNumber);

    setAvatarNumber(newRandomNumber);
  };

  return (
    <Modal
      title={mode == 'add' ? '创建应用' : '编辑应用'}
      open={open}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={submitLoading} onClick={handleSubmit}>
          确定
        </Button>,
      ]}
      width={800}
    >
      <Form form={form} layout="vertical">
        <Form.Item label="配置" name="relevanceAgentInfo" rules={[{ required: true, message: '请选择配置' }]}>
          <Input
            readOnly
            onClick={() => openConfigModal()}
            addonAfter={
              <Space
                onClick={() => openConfigModal()}
                style={{
                  cursor: 'pointer',
                }}
              >
                <SettingOutlined />
                配置
              </Space>
            }
            placeholder="问答智能体（服务：agent-wenda-service）"
          />
        </Form.Item>
        <Flex align="flex-end" justify="space-between">
          <Form.Item
            label="应用名称"
            name="name"
            rules={[{ required: true, message: '请输入应用名称' }]}
            style={{ flex: 1 }}
          >
            <Input placeholder="请输入应用名称" maxLength={30} />
          </Form.Item>
          <div
            style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', marginLeft: 20, width: 60 }}
          >
            <Avatar shape="square" size={60} src={`/images/agentAvatar/${avatarNumber}.png`} />
            <Button size="small" style={{ marginTop: 6 }} onClick={changeAvatar}>
              换一个
            </Button>
          </div>
        </Flex>

        <Form.Item label="描述" name="description">
          <TextArea placeholder="请输入描述" rows={6} maxLength={200} />
        </Form.Item>

        <Form.Item label="是否公开" name="isPublic" initialValue={true}>
          <Radio.Group>
            <Radio value={true}>是</Radio>
            <Radio value={false}>否</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>

      <ConfigModal
        open={isConfigModalOpen}
        onCancel={onConfigModalCancel}
        configSubmit={configSubmit}
        mode={mode}
        baseInfo={baseInfo}
      />
    </Modal>
  );
};

export default AddOrEditAgent;

'use client';
import React, { memo, useEffect, useContext, createContext, useMemo } from 'react';
import { Select, Modal, Form, Input, message, Radio, Button } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import type { FormInstance, FormItemProps } from 'antd';
import { useState } from 'react';
import { agentType, getConfigParam, applyType, testConnect } from '@/services/agent/api';
import { AgentFormData } from '@/types/agent/index';
const { Option } = Select;

interface IProps {
  open: boolean;
  onCancel: () => void;
  configSubmit: (form: FormInstance) => void;
  mode: 'add' | 'edit';
  baseInfo?: AgentFormData;
}

interface agentType {
  code: string;
  name: string;
}

interface ConfigFormData {
  defValue: string; //默认值
  isMast: boolean; //是否必填
  key: string;
  keyName: string;
}

const MyFormItemContext = createContext<(string | number)[]>([]);

interface MyFormItemGroupProps {
  prefix: string | number | (string | number)[];
}

function toArr(str: string | number | (string | number)[]): (string | number)[] {
  return Array.isArray(str) ? str : [str];
}

const MyFormItemGroup: React.FC<React.PropsWithChildren<MyFormItemGroupProps>> = ({ prefix, children }) => {
  const prefixPath = useContext(MyFormItemContext);
  const concatPath = useMemo(() => [...prefixPath, ...toArr(prefix)], [prefixPath, prefix]);

  return <MyFormItemContext.Provider value={concatPath}>{children}</MyFormItemContext.Provider>;
};

const MyFormItem = ({ name, ...props }: FormItemProps) => {
  const prefixPath = useContext(MyFormItemContext);
  const concatName = name !== undefined ? [...prefixPath, ...toArr(name)] : undefined;

  return <Form.Item name={concatName} {...props} />;
};
const ConfigModal: React.FC<IProps> = ({ open, onCancel, configSubmit, mode, baseInfo }) => {
  const [form] = Form.useForm();
  const [options, setOptions] = useState<agentType[]>([]);
  const [applyTypes, setApplyTypes] = useState<agentType[]>([]); //类型
  const [configForm, setConfigForm] = useState<ConfigFormData[]>([]);
  const [type, setType] = useState<string>(''); //类型

  const handleOk = () => {
    configSubmit(form);
  };
  const handleCancel = () => {
    onCancel();
  };

  //获取配置类型
  const getType = async () => {
    const { success, resp, msg } = await agentType();
    if (success) {
      setOptions(resp);
    } else {
      message.error(msg || '查询失败');
    }
  };

  //获取应用类型
  const getApplyType = async () => {
    const { success, resp, msg } = await applyType();
    if (success) {
      setApplyTypes(resp);
    } else {
      message.error(msg || '查询失败');
    }
  };

  const handleTypeChange = (value: string) => {
    setType(value);
    getConfig(value);
  };

  const getConfig = async (type: string) => {
    const { success, resp, msg } = await getConfigParam({
      agentType: type,
    });
    if (success) {
      setConfigForm(resp);
    } else {
      message.error(msg || '查询失败');
    }
  };

  //测试连接
  const testHandle = async () => {
    try {
      form.validateFields().then(async (values) => {
        console.log('values', values);
        const { success } = await testConnect({ source: values.source, ...values.config });
        if (success) {
          message.success('连接成功');
        } else {
          message.error('连接失败');
        }
      });
    } catch (error) {
      message.error('连接失败');
    }
  };

  useEffect(() => {
    getType();
    getApplyType();
  }, []);

  useEffect(() => {
    if (baseInfo) {
      getConfig(baseInfo.source);
      form.setFieldsValue(baseInfo);
      setType(baseInfo.source);
    } else {
      if (open) {
        form.resetFields();
        setConfigForm([]);
      }
    }
  }, [baseInfo, open]);

  return (
    <Modal title="配置智能体" open={open} onOk={handleOk} onCancel={handleCancel} width={600}>
      <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} form={form} style={{ height: 300, paddingTop: 30 }}>
        <Form.Item label="来源" name="source" rules={[{ required: true, message: '请选择!' }]}>
          <Select
            placeholder="请选择来源"
            onChange={(value) => handleTypeChange(value)}
            allowClear
            disabled={mode === 'edit'}
          >
            {!!options.length &&
              options.map((item) => (
                <Option value={item.code} key={item.code} label={item.name}>
                  {item.name}
                </Option>
              ))}
          </Select>
        </Form.Item>
        <MyFormItemGroup prefix={['config']}>
          {!!configForm.length &&
            configForm.map((item) => (
              <MyFormItem
                label={item.key}
                name={item.key}
                initialValue={item.defValue}
                key={item.key}
                rules={[item.isMast ? { required: true, message: '请输入!' } : {}]}
              >
                {type === 'dify' && item.key === 'Authorization' && mode !== 'edit' ? (
                  <Input allowClear addonBefore="Bearer" />
                ) : (
                  <Input allowClear />
                )}
              </MyFormItem>
            ))}
        </MyFormItemGroup>
        <Form.Item label="应用类型" name="applyType" rules={[{ required: true, message: '请选择!' }]}>
          <Radio.Group>
            {!!applyTypes.length &&
              applyTypes.map((item) => (
                <Radio value={item.code} key={item.code}>
                  {item.name}
                </Radio>
              ))}
          </Radio.Group>
        </Form.Item>
        <Form.Item>
          <Button style={{ marginLeft: 35 }} onClick={testHandle} type="link" icon={<QuestionCircleOutlined />}>
            测试连接
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default memo(ConfigModal);

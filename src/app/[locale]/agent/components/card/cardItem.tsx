'use client';
import React, { useState, memo } from 'react';
import { Card, Modal, Tag, message, Dropdown, Avatar } from 'antd';
import { MoreOutlined } from '@ant-design/icons';
import data from '@emoji-mart/data';
import { init } from 'emoji-mart';
import { CardData } from '@/types/agent/index';
import type { MenuProps } from 'antd';
import { useRouter } from 'next/navigation';
import { useAgentListContext } from '@/contexts/agentListContext';
import { deleteAgent } from '@/services/agent/api';
import StyleSheet from './cardItem.module.less';

// 初始化emoji-mart
init({ data });

interface CardItemProps {
  data: CardData;
}

const items: MenuProps['items'] = [
  {
    label: '编辑',
    key: '1',
  },
  {
    label: '删除',
    key: '2',
  },
];

const CardItem: React.FC<CardItemProps> = ({ data }) => {
  const { setPopOpen, setInitialValues, setMode, getList } = useAgentListContext() ?? {};
  const router = useRouter();
  const [show, setShow] = useState<Boolean>(false);

  const onClick: MenuProps['onClick'] = async ({ key }) => {
    if (key === '1') {
      // 编辑
      setPopOpen && setPopOpen(true);
      setMode && setMode('edit');
      setInitialValues && setInitialValues(data);
    }
    if (key === '2') {
      //删除
      Modal.confirm({
        title: '确认删除',
        content: `确认删除该智能体：${data.name}?`,
        okText: '确认',
        cancelText: '取消',
        okButtonProps: {
          danger: true,
        },
        async onOk() {
          const respData = await deleteAgent(data.id);
          if (respData.success) {
            message.success('删除成功');
            getList && getList();
          }
        },
      });
    }
  };

  return (
    <div className={StyleSheet.cardWrapper}>
      <Card className={StyleSheet.card} onMouseEnter={() => setShow(true)} onMouseLeave={() => setShow(false)}>
        <div
          onClick={(e) => {
            e.stopPropagation();
            router.push(`/agent/${data.id}/operate?source=${data.source}&applyType=${data.applyType}`);
          }}
        >
          <div className={StyleSheet.head}>
            <div className={`${StyleSheet.icon} ${show ? StyleSheet['animate__pulse'] : ''}`}>
              <Avatar
                shape="square"
                size={60}
                src={`/images/agentAvatar/${data.avatar ?? 1}.png`}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  objectFit: 'contain',
                }}
              />
            </div>
            <div className={StyleSheet.name}>
              <span className={StyleSheet.nameTitle}>{data.name}</span>
              <Tag>{data.sourceName ?? '暂无信息'}</Tag>
              <Tag>{data.applyTypeName ?? '暂无信息'}</Tag>
            </div>
          </div>
          <div className={StyleSheet.desc}>{data.description}</div>
          <div className={StyleSheet.footer}>
            <div>@{data.createByName}</div>
            <div>创建于{data.createdAt}</div>
          </div>
        </div>
        <div className={`${StyleSheet.others}`}>
          <Dropdown menu={{ items, onClick }}>
            <a onClick={(e) => e.preventDefault()}>
              <MoreOutlined style={{ color: '#666', fontSize: '16px', fontWeight: 'bold' }} />
            </a>
          </Dropdown>
        </div>
      </Card>
    </div>
  );
};

export default memo(CardItem);

'use client';
import Layout from '@/components/Layout';
import { deletePropmt, query } from '@/services/prompt/api';
import useStore from '@/store/useStore';
import { roleJudgment } from '@/utils/index';
import { EllipsisOutlined, PlusOutlined, RightOutlined } from '@ant-design/icons';
import { Button, Card, Dropdown, Input, message, Modal, Pagination, Space, Typography, Empty } from 'antd';
import { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import StyleSheet from '../page.module.less';
import PromptModal from './components/PromptModal';
import { displayGridData } from './constant';

const { Title } = Typography;
const { confirm } = Modal;

// 定义类型
interface PromptTemplate {
  id: number;
  name: string;
  content: string;
  type: string;
  createTime: string;
  author: string;
}

interface QueryParams {
  params?: {
    name?: string;
  };
  current?: number;
  pageSize?: number;
}

export default function PromptPage() {
  const { userInfo } = useStore();
  const router = useRouter();

  const [searchName, setSearchName] = useState('');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<PromptTemplate[]>([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<Partial<PromptTemplate>>();
  const [modalMode, setModalMode] = useState<'add' | 'edit' | 'view'>('add');
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(35);

  // 初始化加载
  useEffect(() => {
    handleSearch({ params: { name: searchName }, current, pageSize });
  }, [searchName, current, pageSize]);

  // 处理搜索
  const handleSearch = async (params: QueryParams = {}) => {
    try {
      setLoading(true);
      const { resp, success, msg } = await query(params).finally(() => {
        setLoading(false);
      });

      if (success && resp?.length > 0) {
        setData(resp[0].list || []);
        setTotal(resp[0].pagination?.total || 0);
      } else {
        message.error(msg || '查询失败');
      }
    } catch (error) {
      message.error('查询失败');
    }
  };

  // 分页改变
  const handlePageChange = (page: number, pageSize: number) => {
    setCurrent(page);
    setPageSize(pageSize);
  };

  // 打开模态框 - 新增
  const handleAdd = () => {
    setModalMode('add');
    setCurrentRecord(undefined);
    setModalOpen(true);
  };

  // 操作：查看/编辑/删除
  const handleOperate = async (e: React.MouseEvent, key: string, data: PromptTemplate) => {
    e.preventDefault();
    switch (key) {
      case '1':
        setModalMode('view');
        setCurrentRecord(data);
        setModalOpen(true);
        break;
      case '2':
        setModalMode('edit');
        setCurrentRecord(data);
        setModalOpen(true);
        break;
      case '3':
        confirm({
          title: '确认删除',
          content: `是否删除该模板：${data.name}?`,
          okText: '确认',
          cancelText: '取消',
          okButtonProps: { danger: true },
          onOk: async () => {
            try {
              const { success, msg } = await deletePropmt({ id: data.id });
              if (success) {
                message.success(msg);
                handleSearch({ params: { name: searchName }, current, pageSize });
              }
            } catch (error) {
              message.error('删除失败');
            }
          },
        });
        break;
      default:
        break;
    }
  };

  // 生成下拉菜单
  const generateItems = (record: PromptTemplate) => {
    return [
      {
        label: '查看',
        key: '1',
        auth: true,
      },
      {
        label: '编辑',
        key: '2',
        auth: roleJudgment(userInfo, 'PROMPT_EDIT'),
      },
      {
        label: '删除',
        key: '3',
        auth: roleJudgment(userInfo, 'PROMPT_DELTE'),
      },
    ]
      .filter((item) => item.auth)
      .map((item) => ({
        key: item.key,
        label: <a onClick={(e) => handleOperate(e, item.key, record)}>{item.label}</a>,
      }));
  };

  // 模态框成功回调
  const handleModalSuccess = () => {
    setModalOpen(false);
    handleSearch({ params: { name: searchName }, current, pageSize });
  };

  return (
    <Layout curActive="/prompt/list">
      <div className="p-6">
        <Title level={2} style={{ marginBottom: 24 }}>
          提示词模板
        </Title>

        {/* 卡片区域 */}
        <div className={StyleSheet.topbar}>
          {displayGridData.map((item, index) => (
            <div
              className={StyleSheet.card}
              key={index}
              onClick={(e) => {
                e.stopPropagation();
                router.push(item.path);
              }}
            >
              <div className={StyleSheet.lefticon}>
                <Image src={item.image} width={56} height={56} alt={item.name} />
              </div>
              <div className={StyleSheet.rightcontent}>
                <div className={StyleSheet.righthead}>
                  <span className={StyleSheet.righttitle}>{item.name}</span>
                  <div className={StyleSheet.righticon}>
                    <Link href={item.path}>
                      <RightOutlined />
                    </Link>
                  </div>
                </div>
                <div className={StyleSheet.rightbody}>{item.content}</div>
              </div>
            </div>
          ))}
        </div>

        {/* 数据表格卡片 */}
        <Card bordered={false}>
          <div style={{ marginBottom: 24 }}>
            <Space>
              {roleJudgment(userInfo, 'PROMPT_ADD') && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  创建模板
                </Button>
              )}
              <Input.Search
                placeholder="请输入模板名称"
                style={{ width: 260 }}
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                onSearch={() => handleSearch({ params: { name: searchName }, current, pageSize })}
                allowClear
              />
            </Space>
          </div>

          {/* 列表展示 */}
          {/* 列表展示 */}
          <div className={`${data.length ? StyleSheet.cardList : StyleSheet.empty}`}>
            {data.length ? (
              data.map((item, index) => (
                <div className={StyleSheet.cardListItem} key={index}>
                  <div className={StyleSheet.cardListItemTitleLine}>
                    <span className={StyleSheet.cardListItemTitle} onClick={(e) => handleOperate(e, '1', item)}>
                      {item.name}
                    </span>
                    <div className={StyleSheet.cardListItemExtra}>
                      <Dropdown menu={{ items: generateItems(item) }}>
                        <EllipsisOutlined />
                      </Dropdown>
                    </div>
                  </div>
                  <div className={StyleSheet.cardListItemLable}>{item.type}</div>
                  <div className={StyleSheet.cardListItemDesc} onClick={(e) => handleOperate(e, '1', item)}>
                    {item.content}
                  </div>
                </div>
              ))
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </div>

          {/* 分页器 */}
          <div className={StyleSheet.pagerbar}>
            <Pagination
              total={total}
              showTitle
              showSizeChanger
              showQuickJumper
              defaultPageSize={35}
              pageSizeOptions={[35, 45, 55]}
              pageSize={pageSize}
              current={current}
              onChange={handlePageChange}
              onShowSizeChange={handlePageChange}
              showTotal={(total) => `共 ${total} 条`}
            />
          </div>
        </Card>
        {modalOpen && (
          <PromptModal
            open={modalOpen}
            mode={modalMode}
            initialValues={currentRecord}
            onCancel={() => setModalOpen(false)}
            onSuccess={handleModalSuccess}
          />
        )}
      </div>
    </Layout>
  );
}

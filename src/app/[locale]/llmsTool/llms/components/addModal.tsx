'use client';
import { useState } from 'react';
import { Modal, Form, Input, Select, Switch, Upload, Button, message } from 'antd';
const { TextArea } = Input;
import { PlusOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';

interface IProps {
    open: boolean;
    onCancel: () => void;
    onOk: (values: any) => void;
    modelList: any[];
}

const AddModal: React.FC<IProps> = ({ open, onCancel, onOk, modelList }) => {
    const [form] = Form.useForm();
    const [fileList, setFileList] = useState<UploadFile[]>([]);

    // 处理图标上传
    const handleUploadChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
        setFileList(newFileList);
    };

    // 上传前的处理
    const beforeUpload = (file: File) => {
        const isImage = file.type.startsWith('image/');
        if (!isImage) {
            message.error('只能上传图片文件!');
            return false;
        }
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isLt2M) {
            message.error('图片大小不能超过 2MB!');
            return false;
        }
        return false; // 阻止自动上传，只做预览
    };

    // 提交表单
    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();

            // 处理图标数据
            if (fileList.length > 0) {
                // 这里可以处理图标上传逻辑
                values.icon = fileList[0].url || fileList[0].thumbUrl;
            }

            onOk(values);
            form.resetFields();
            setFileList([]);
        } catch (error) {
            console.error('表单验证失败:', error);
        }
    };

    // 取消操作
    const handleCancel = () => {
        form.resetFields();
        setFileList([]);
        onCancel();
    };

    // 生成品牌选项
    const getBrandOptions = () => {
        return modelList.map((item: any) => ({
            label: item.label || item.title,
            value: item.brand || item.key,
        }));
    };

    const uploadButton = (
        <div>
            <PlusOutlined />
            <div style={{ marginTop: 8 }}>上传</div>
        </div>
    );

    return (
        <Modal
            title="新增"
            open={open}
            onCancel={handleCancel}
            onOk={handleSubmit}
            width={600}
            okText="确定"
            cancelText="取消"
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={{
                    supportChat: false,
                    supportFunctionCalling: false,
                    supportEmbed: false,
                    supportReranker: false,
                    supportVision: false,
                    supportAudio: false,
                    supportFileSearch: false,
                    supportSystemPrompt: false,
                    supportImageGeneration: false,
                    supportCodeGeneration: false,
                }}
            >
                {/* Icon 上传 */}
                <Form.Item label="Icon:" name="icon">
                    <Upload
                        listType="picture-card"
                        fileList={fileList}
                        onChange={handleUploadChange}
                        beforeUpload={beforeUpload}
                        maxCount={1}
                    >
                        {fileList.length >= 1 ? null : uploadButton}
                    </Upload>
                </Form.Item>

                {/* 名称 */}
                <Form.Item
                    label="名称:"
                    name="title"
                    rules={[{ required: true, message: '请输入名称' }]}
                >
                    <Input placeholder="请输入名称" />
                </Form.Item>

                {/* 品牌 */}
                <Form.Item
                    label="品牌:"
                    name="brand"
                    rules={[{ required: true, message: '请选择品牌' }]}
                >
                    <Select placeholder="请选择品牌" options={getBrandOptions()} />
                </Form.Item>

                {/* 请求地址 */}
                <Form.Item
                    label="请求地址:"
                    name="llmEndpoint"
                    rules={[{ required: true, message: '请输入请求地址' }]}
                >
                    <Input placeholder="请输入请求地址" />
                </Form.Item>

                {/* 模型名称 */}
                <Form.Item
                    label="模型名称:"
                    name="llmModel"
                    rules={[{ required: true, message: '请输入模型名称' }]}
                >
                    <Input placeholder="请输入模型名称" />
                </Form.Item>

                {/* API Key */}
                <Form.Item
                    label="API Key:"
                    name="llmApiKey"
                    rules={[{ required: true, message: '请输入API Key' }]}
                >
                    <Input.Password placeholder="请输入API Key" />
                </Form.Item>

                {/* 其他配置 */}
                <Form.Item label="其他配置:" name="llmExtraConfig">
                    <TextArea
                        rows={4}
                        placeholder="此处已 property 形式进行配置，例如：appId=123456"
                    />
                </Form.Item>

                {/* 描述 */}
                <Form.Item label="描述:" name="description">
                    <TextArea rows={3} placeholder="请输入描述" />
                </Form.Item>

                {/* 功能开关 */}
                <Form.Item label="对话模型:" name="supportChat" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="方法调用:" name="supportFunctionCalling" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="向量化:" name="supportEmbed" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="重排:" name="supportReranker" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="文生图:" name="supportTextToImage" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="图生图:" name="supportImageToImage" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="文生音频:" name="supportTextToAudio" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="音频转音频:" name="supportAudioToAudio" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="文生视频:" name="supportTextToVideo" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="图片转视频:" name="supportImageToVideo" valuePropName="checked">
                    <Switch />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default AddModal;

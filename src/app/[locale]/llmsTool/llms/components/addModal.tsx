'use client';
import { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Switch, Upload, message } from 'antd';
const { TextArea } = Input;
import { PlusOutlined } from '@ant-design/icons';
import type { GetProp, UploadFile, UploadProps } from 'antd';
import { save, update } from '@/services/llms/api';
import { updateLLMModelData, LLMModelConfig } from '@/types/llms/index';
import ImgCrop from 'antd-img-crop';
import Cookies from 'js-cookie';

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

interface IProps {
    open: boolean;
    onCancel: () => void;
    onOk: () => void;
    modelList: any[];
    editData?: LLMModelConfig | null; // 编辑时的数据
    isEdit?: boolean; // 是否为编辑模式
}

const AddModal: React.FC<IProps> = ({ open, onCancel, onOk, modelList, editData, isEdit = false }) => {
    const [form] = Form.useForm();
    // const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [loading, setLoading] = useState(false);
    const [imageUrl, setImageUrl] = useState<string>();
    //     const [fileList, setFileList] = useState<UploadFile[]>([
    //     {
    //         uid: '-1',
    //         name: 'image.png',
    //         status: 'done',
    //         url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
    //     },
    // ]);

    // 当编辑数据变化时，填充表单
    useEffect(() => {
        if (open && isEdit && editData) {
            // 填充表单数据
            form.setFieldsValue({ ...editData });
        } else if (open && !isEdit) {
            // 新增时重置表单
            form.resetFields();
            setImageUrl("");
        }
    }, [open, isEdit, editData, form]);




    // 提交表单
    const handleSubmit = async () => {
        try {
            setLoading(true);
            const values = await form.validateFields();

            // 处理图标数据
            // if (fileList.length > 0) {
            //     values.icon = fileList[0].url || fileList[0].thumbUrl;
            // }

            if (isEdit && editData) {
                // 编辑模式
                const updateData: updateLLMModelData = {
                    id: editData.id,
                    ...values,
                };

                const { errorCode } = await update(updateData);
                if (errorCode === 0) {
                    message.success('编辑成功');
                    handleCancel();
                    onOk();
                } else {
                    message.error('编辑失败');
                }
            } else {
                // 新增模式
                const { errorCode } = await save({ ...values });
                if (errorCode === 0) {
                    message.success('新增成功');
                    handleCancel();
                    onOk();
                } else {
                    message.error('新增失败');
                }
            }
        } catch (error) {
            console.error('操作失败:', error);
            message.error('操作失败');
        } finally {
            setLoading(false);
        }
    };

    // 取消操作
    const handleCancel = () => {
        form.resetFields();
        // setFileList([]);
        onCancel();
    };

    // 生成品牌选项
    const getBrandOptions = () => {
        return modelList.map((item: any) => ({
            label: item.label || item.title,
            value: item.brand || item.key,
        }));
    };

    //上传头像部分--- start
    const getBase64 = (img: FileType, callback: (url: string) => void) => {
        const reader = new FileReader();
        reader.addEventListener('load', () => callback(reader.result as string));
        reader.readAsDataURL(img);
    };

    const beforeUpload = (file: FileType) => {
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isLt2M) {
            message.error('文件大小不能超过 2MB!');
        }
        return isLt2M;
    };

    const handleChange: UploadProps['onChange'] = (info) => {
        if (info.file.status === 'uploading') {
            setLoading(true);
            return;
        }
        if (info.file.status === 'done') {
            // Get this url from response in real world.
            getBase64(info.file.originFileObj as FileType, (url) => {
                setLoading(false);
                setImageUrl(url);
            });
        }
    };

    const uploadButton = (
        <div>
            <PlusOutlined />
            <div style={{ marginTop: 8 }}>上传</div>
        </div>
    );
    //上传头像部分--- end



    return (
        <Modal
            title={isEdit ? '编辑' : '新增'}
            open={open}
            onCancel={handleCancel}
            onOk={handleSubmit}
            width={600}
            okText="确定"
            cancelText="取消"
            confirmLoading={loading}
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={{
                    supportChat: false,
                    supportFunctionCalling: false,
                    supportEmbed: false,
                    supportReranker: false,
                    supportTextToImage: false,
                    supportImageToImage: false,
                    supportTextToAudio: false,
                    supportAudioToAudio: false,
                    supportTextToVideo: false,
                    supportImageToVideo: false,
                }}
            >
                {/* Icon 上传 */}
                <Form.Item label="Icon:" name="icon">
                    <ImgCrop rotationSlider>
                        <Upload
                            name="avatar"
                            listType="picture-circle"
                            showUploadList={false}
                            accept="image/png, image/jpeg, image/jpg, image/bmp, image/webp, image/svg"
                            action={process.env.NEXT_PUBLIC_AI_BASE_URL + "/ai-flow/api/v1/commons/upload"}
                            beforeUpload={beforeUpload}
                            onChange={handleChange}
                            headers={{ Authorization: Cookies.get('access_token') as string }}
                        >
                            {imageUrl ? <img src={imageUrl} alt="avatar" style={{ width: '100%' }} /> : uploadButton}
                        </Upload>
                    </ImgCrop>
                </Form.Item>

                {/* 名称 */}
                <Form.Item label="名称:" name="title" rules={[{ required: true, message: '请输入名称' }]}>
                    <Input placeholder="请输入名称" />
                </Form.Item>

                {/* 品牌 */}
                <Form.Item label="品牌:" name="brand" rules={[{ required: true, message: '请选择品牌' }]}>
                    <Select placeholder="请选择品牌" options={getBrandOptions()} />
                </Form.Item>

                {/* 请求地址 */}
                <Form.Item label="请求地址:" name="llmEndpoint" rules={[{ required: true, message: '请输入请求地址' }]}>
                    <Input placeholder="请输入请求地址" />
                </Form.Item>

                {/* 模型名称 */}
                <Form.Item label="模型名称:" name="llmModel" rules={[{ required: true, message: '请输入模型名称' }]}>
                    <Input placeholder="请输入模型名称" />
                </Form.Item>

                {/* API Key */}
                <Form.Item label="API Key:" name="llmApiKey" rules={[{ required: true, message: '请输入API Key' }]}>
                    <Input placeholder="请输入API Key" />
                </Form.Item>

                {/* 其他配置 */}
                <Form.Item label="其他配置:" name="llmExtraConfig">
                    <TextArea rows={4} placeholder="此处已 property 形式进行配置，例如：appId=123456" />
                </Form.Item>

                {/* 描述 */}
                <Form.Item label="描述:" name="description">
                    <TextArea rows={3} placeholder="请输入描述" />
                </Form.Item>

                {/* 功能开关 */}
                <Form.Item label="对话模型:" name="supportChat" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="方法调用:" name="supportFunctionCalling" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="向量化:" name="supportEmbed" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="重排:" name="supportReranker" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="文生图:" name="supportTextToImage" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="图生图:" name="supportImageToImage" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="文生音频:" name="supportTextToAudio" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="音频转音频:" name="supportAudioToAudio" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="文生视频:" name="supportTextToVideo" valuePropName="checked">
                    <Switch />
                </Form.Item>

                <Form.Item label="图片转视频:" name="supportImageToVideo" valuePropName="checked">
                    <Switch />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default AddModal;

'use client';
import { useEffect, useState, useRef } from 'react';
import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, Space, Tag, Table, message } from 'antd';
import { LLMModelConfig as TableListItem } from "@/types/llms"
import { getAiLlmList } from "@/services/llms/api"
import type { GetProp, MenuProps } from 'antd';
type MenuItem = GetProp<MenuProps, 'items'>[number];

interface IProps {
    modelList: MenuItem[];
    selectBrand: string
}
const TableList: React.FC<IProps> = ({ modelList, selectBrand }) => {
    const actionRef = useRef<ActionType>();
    const [dataSource, setDataSource] = useState<TableListItem[]>([]);

    // 将 modelList 转换为 valueEnum 格式
    const getBrandValueEnum = () => {
        const valueEnum: Record<string, { text: string; status?: string }> = {};

        modelList.forEach((item: any) => {
            const key = item.key;
            const text = item.label || item.title;

            if (key && text) {
                valueEnum[key] = {
                    text: text,
                };
            }
        });

        return valueEnum;
    };

    const columns: ProColumns<TableListItem>[] = [
        {
            dataIndex: 'index',
            width: 48,
            search: false,
        },
        {
            title: 'Icon',
            dataIndex: 'icon',
            width: 48,
            search: false,
            render: (text: any) => (
                <img src={text} alt="" style={{ width: 30, height: 30 }} />
            ),
        },
        {
            title: '名称',
            dataIndex: 'title',
            width: 150,
            ellipsis: true,
        },
        {
            title: '品牌',
            dataIndex: 'brand',
            ellipsis: true,
            width: 150,
            valueType: 'select',
            valueEnum: getBrandValueEnum(),
        },
        {
            title: "能力",
            dataIndex: "supportFeatures",
            search: false,
            width: 200,
            render: (features: any) => {
                if (!features) {
                    return "";
                }

                return features.map((feature: any) => <Tag key={feature}>{feature}</Tag>);
            },
        },
        {
            title: "描述",
            dataIndex: "description",
            search: false,
            width: "20%",
        },
        {
            title: '操作',
            valueType: 'option',
            key: 'option',
            width: 150,
            render: (_, record, __, action) => [
                <a
                    key="editable"
                    onClick={() => {
                        action?.startEditable?.(record.id);
                    }}
                >
                    编辑
                </a>,
                <a style={{ color: '#ff4d4f' }} key="del">
                    删除
                </a>,
            ],
        },
    ];

    //查询列表数据
    const fetchTableData = async () => {
        const { data, errorCode } = await getAiLlmList({
            brand: "",
            title: "",
            pageNumber: 1,
            pageSize: 10,
        });
        if (errorCode === 0) {
            setDataSource(data?.records || []);
        } else {
            message.error("获取列表失败");
        }
    }

    useEffect(() => {
        fetchTableData();
    }, [])



    return (
        <ProTable<TableListItem>
            columns={columns}
            actionRef={actionRef}
            rowSelection={{
                selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
                defaultSelectedRowKeys: [],
            }}
            tableAlertRender={({
                selectedRowKeys,
                selectedRows,
                onCleanSelected,
            }) => {
                console.log(selectedRowKeys, selectedRows);
                return (
                    <Space size={24}>
                        <span>
                            已选 {selectedRowKeys.length} 项
                            <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                                取消选择
                            </a>
                        </span>
                    </Space>
                );
            }}
            tableAlertOptionRender={() => {
                return <a>批量删除</a>
            }}
            dataSource={dataSource}
            columnsState={{
                persistenceKey: 'pro-table-singe-demos',
                persistenceType: 'localStorage',
                defaultValue: {
                    option: { fixed: 'right', disable: true },
                },
                onChange(value) {
                    console.log('value: ', value);
                },
            }}
            rowKey="id"
            search={{
                labelWidth: 'auto',
            }}
            options={{
                setting: {
                    listsHeight: 400,
                },
            }}
            pagination={{
                showSizeChanger: true,
            }}
            // headerTitle="高级表格"
            toolBarRender={() => [
                <Button
                    key="button"
                    icon={<PlusOutlined />}
                    onClick={() => {
                        actionRef.current?.reload();
                    }}
                    type="primary"
                >
                    新增
                </Button>
            ]}
        />
    );
};

export default TableList;

'use client';
import { useEffect, useState, useRef } from 'react';
import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, Space, Tag, Table, message } from 'antd';
import { LLMModelConfig as TableListItem } from "@/types/llms"
import { getAiLlmList } from "@/services/llms/api"
import type { GetProp, MenuProps } from 'antd';
type MenuItem = GetProp<MenuProps, 'items'>[number];

interface IProps {
    modelList: MenuItem[];
    selectBrand: string
}
const TableList: React.FC<IProps> = ({ modelList, selectBrand }) => {
    const actionRef = useRef<ActionType>();
    const [dataSource, setDataSource] = useState<TableListItem[]>([]);

    const columns: ProColumns<TableListItem>[] = [
        {
            dataIndex: 'index',
            width: 48,
            search: false,
        },
        {
            title: 'Icon',
            dataIndex: 'icon',
            width: 48,
            search: false,
            render: (text: any) => (
                <img src={text} alt="" style={{ width: 30, height: 30 }} />
            ),
        },
        {
            title: '名称',
            dataIndex: 'title',
            width: 150,
            ellipsis: true,
        },
        {
            title: '品牌',
            dataIndex: 'brand',
            ellipsis: true,
            width: 150,
            valueType: 'select',
            valueEnum: {
                open: {
                    text: '未解决',
                    status: 'Error',
                },
                closed: {
                    text: '已解决',
                    status: 'Success',
                    disabled: true,
                },
                processing: {
                    text: '解决中',
                    status: 'Processing',
                },
            },
        },
        {
            title: "能力",
            dataIndex: "supportFeatures",
            search: false,
            width: 200,
            render: (features: any) => {
                if (!features) {
                    return "";
                }

                return features.map((feature: any) => <Tag key={feature}>{feature}</Tag>);
            },
        },
        {
            title: "描述",
            dataIndex: "description",
            search: false,
            width: "20%",
        },
        {
            title: '操作',
            valueType: 'option',
            key: 'option',
            width: 150,
            render: (text, record, _, action) => [
                <a
                    key="editable"
                    onClick={() => {
                        action?.startEditable?.(record.id);
                    }}
                >
                    编辑
                </a>,
                <a href={record.url} target="_blank" rel="noopener noreferrer" key="view">
                    查看
                </a>,
            ],
        },
    ];

    //查询列表数据
    const fetchTableData = async () => {
        const { data, errorCode } = await getAiLlmList({
            brand: "",
            title: "",
            pageNumber: 1,
            pageSize: 10,
        });
        if (errorCode === 0) {
            setDataSource(data?.records || []);
        } else {
            message.error("获取列表失败");
        }
    }

    useEffect(() => {
        fetchTableData();
    }, [])



    return (
        <ProTable<TableListItem>
            columns={columns}
            actionRef={actionRef}
            rowSelection={{
                selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
                defaultSelectedRowKeys: [1],
            }}
            tableAlertRender={({
                selectedRowKeys,
                selectedRows,
                onCleanSelected,
            }) => {
                console.log(selectedRowKeys, selectedRows);
                return (
                    <Space size={24}>
                        <span>
                            已选 {selectedRowKeys.length} 项
                            <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                                取消选择
                            </a>
                        </span>
                    </Space>
                );
            }}
            tableAlertOptionRender={() => {
                return (
                    <Space size={16}>
                        <a>批量删除</a>
                        <a>导出数据</a>
                    </Space>
                );
            }}
            dataSource={dataSource}
            columnsState={{
                persistenceKey: 'pro-table-singe-demos',
                persistenceType: 'localStorage',
                defaultValue: {
                    option: { fixed: 'right', disable: true },
                },
                onChange(value) {
                    console.log('value: ', value);
                },
            }}
            rowKey="id"
            search={{
                labelWidth: 'auto',
            }}
            options={{
                setting: {
                    listsHeight: 400,
                },
            }}
            pagination={{
                showSizeChanger: true,
            }}
            dateFormatter="string"
            // headerTitle="高级表格"
            toolBarRender={() => [
                <Button
                    key="button"
                    icon={<PlusOutlined />}
                    onClick={() => {
                        actionRef.current?.reload();
                    }}
                    type="primary"
                >
                    新建
                </Button>,
                <Dropdown
                    key="menu"
                    menu={{
                        items: [
                            {
                                label: '1st item',
                                key: '1',
                            },
                            {
                                label: '2nd item',
                                key: '2',
                            },
                            {
                                label: '3rd item',
                                key: '3',
                            },
                        ],
                    }}
                >
                    <Button>
                        <EllipsisOutlined />
                    </Button>
                </Dropdown>,
            ]}
        />
    );
};

export default TableList;

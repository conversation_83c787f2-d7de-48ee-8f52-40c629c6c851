'use client';
import { useRef, useEffect, useState } from 'react';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, Space, Tag, message, Popconfirm } from 'antd';
import { LLMModelConfig as TableListItem } from '@/types/llms';
import { getAiLlmList, remove, removeBatch } from '@/services/llms/api';
import type { GetProp, MenuProps } from 'antd';
import AddModal from './addModal';
type MenuItem = GetProp<MenuProps, 'items'>[number];

interface IProps {
  modelList: MenuItem[];
  selectBrand: string;
}
const TableList: React.FC<IProps> = ({ modelList, selectBrand }) => {
  const actionRef = useRef<ActionType>();
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [editData, setEditData] = useState<TableListItem | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  // 将 modelList 转换为 valueEnum 格式
  const getBrandValueEnum = () => {
    const valueEnum: Record<string, { text: string; status?: string }> = {};

    modelList.forEach((item: any) => {
      const key = item.key;
      const text = item.label || item.title;

      if (key && text) {
        valueEnum[key] = {
          text: text,
        };
      }
    });

    return valueEnum;
  };

  const columns: ProColumns<TableListItem>[] = [
    {
      title: 'Icon',
      dataIndex: 'icon',
      width: 48,
      search: false,
      render: (text: any) => <img src={text} alt="" style={{ width: 30, height: 30 }} />,
    },
    {
      title: '名称',
      dataIndex: 'title',
      width: 150,
      ellipsis: true,
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      ellipsis: true,
      search: false,
      width: 150,
      valueType: 'select',
      valueEnum: getBrandValueEnum(),
    },
    {
      title: '能力',
      dataIndex: 'supportFeatures',
      search: false,
      width: 200,
      render: (features: any) => {
        if (!features) {
          return '';
        }

        return features.map((feature: any) => <Tag key={feature}>{feature}</Tag>);
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      search: false,
      width: '20%',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 150,
      render: (_, record) => [
        <a key="edit" onClick={() => handleEdit(record)}>
          编辑
        </a>,
        <Popconfirm
          title="确认删除？"
          description="您确定要删除这条数据吗？"
          onConfirm={() => handleDelete(record.id)}
          okText="确认"
          cancelText="取消"
        >
          <a style={{ color: '#ff4d4f' }} key="del">
            删除
          </a>
        </Popconfirm>,
      ],
    },
  ];

  // 刷新表格（删除后自动处理分页）
  const smartReload = () => {
    actionRef.current?.reload();
  };

  // 单个删除
  const handleDelete = async (id: string) => {
    try {
      const { errorCode } = await remove(id);
      if (errorCode === 0) {
        message.success('删除成功');
        // 刷新表格
        smartReload();
      } else {
        message.error('删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async (selectedRowKeys: React.Key[], callback: () => void) => {
    try {
      const ids = selectedRowKeys.map((key) => String(key));
      const { errorCode } = await removeBatch(ids);
      if (errorCode === 0) {
        message.success('批量删除成功');
        // 刷新表格
        smartReload();
        callback();
      } else {
        message.error('批量删除失败');
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败');
    }
  };
  // 查询列表数据的方法
  const fetchTableData = async (params: any) => {
    try {
      // 处理品牌筛选逻辑
      let brandFilter = '';

      // 如果左侧菜单选择了品牌，优先使用菜单选择的品牌
      if (selectBrand && selectBrand !== 'all') {
        // 'all' 是"全部数据"的key
        brandFilter = selectBrand;
      }

      let currentPage = params.current || 1;
      const pageSize = params.pageSize || 10;

      const { data, errorCode } = await getAiLlmList({
        brand: brandFilter,
        title: params.title || '',
        pageNumber: currentPage,
        pageSize: pageSize,
      });

      if (errorCode === 0) {
        const records = data?.records || [];
        const total = data?.totalRow || 0;

        // 如果当前页没有数据且当前页大于1，则查询前一页
        if (records.length === 0 && currentPage > 1 && total > 0) {
          const maxPage = Math.ceil(total / pageSize);
          const targetPage = Math.min(currentPage - 1, maxPage);

          if (targetPage > 0) {
            // 递归查询前一页
            return await fetchTableData({
              ...params,
              current: targetPage,
            });
          }
        }

        return {
          data: records,
          success: true,
          total: total,
          current: currentPage,
        };
      } else {
        message.error('获取列表失败');
        return {
          data: [],
          success: false,
          total: 0,
        };
      }
    } catch (error) {
      message.error('查询失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  // 重置查询条件
  const handleReset = () => {
    actionRef.current?.reset?.();
  };

  // 手动触发查询
  const handleSearch = () => {
    smartReload();
  };

  // 监听左侧菜单选择变化，自动刷新表格
  useEffect(() => {
    smartReload();
  }, [selectBrand]);

  // 打开新增弹窗
  const handleAdd = () => {
    setIsEdit(false);
    setEditData(null);
    setAddModalOpen(true);
  };

  // 打开编辑弹窗
  const handleEdit = (record: TableListItem) => {
    setIsEdit(true);
    setEditData(record);
    setAddModalOpen(true);
  };

  // 关闭弹窗
  const handleAddCancel = () => {
    setAddModalOpen(false);
    setIsEdit(false);
    setEditData(null);
  };

  // 处理提交（新增或编辑）
  const handleAddSubmit = () => {
    // 刷新表格
    smartReload();
  };

  return (
    <>
      <ProTable<TableListItem>
        columns={columns}
        actionRef={actionRef}
        request={fetchTableData}
        rowSelection={{}}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => {
          return (
            <Space size={24}>
              <span>
                已选 {selectedRowKeys.length} 项
                <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                  取消选择
                </a>
              </span>
              <Popconfirm
                title="确认删除？"
                description={`您确定要删除所选的 ${selectedRowKeys.length} 条数据吗？`}
                onConfirm={() => handleBatchDelete(selectedRowKeys, onCleanSelected)}
                okText="确认"
                cancelText="取消"
              >
                <a style={{ color: '#ff4d4f' }}>
                  <DeleteOutlined /> 批量删除
                </a>
              </Popconfirm>
            </Space>
          );
        }}
        tableAlertOptionRender={() => {
          return null;
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
        onSubmit={handleSearch}
        onReset={handleReset}
        headerTitle={
          <Button key="add" icon={<PlusOutlined />} onClick={handleAdd} type="primary">
            新增
          </Button>
        }
      />

      {/* 新增/编辑弹窗 */}
      <AddModal
        open={addModalOpen}
        onCancel={handleAddCancel}
        onOk={handleAddSubmit}
        modelList={modelList}
        editData={editData}
        isEdit={isEdit}
      />
    </>
  );
};

export default TableList;

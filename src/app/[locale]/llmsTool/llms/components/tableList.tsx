'use client';
import { useRef, useEffect } from 'react';
import { EllipsisOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, Space, Tag, Table, message } from 'antd';
import { LLMModelConfig as TableListItem } from "@/types/llms"
import { getAiLlmList } from "@/services/llms/api"
import type { GetProp, MenuProps } from 'antd';
type MenuItem = GetProp<MenuProps, 'items'>[number];

interface IProps {
    modelList: MenuItem[];
    selectBrand: string
}
const TableList: React.FC<IProps> = ({ modelList, selectBrand }) => {
    const actionRef = useRef<ActionType>();
    // 将 modelList 转换为 valueEnum 格式
    const getBrandValueEnum = () => {
        const valueEnum: Record<string, { text: string; status?: string }> = {};

        modelList.forEach((item: any) => {
            const key = item.key;
            const text = item.label || item.title;

            if (key && text) {
                valueEnum[key] = {
                    text: text,
                };
            }
        });

        return valueEnum;
    };

    const columns: ProColumns<TableListItem>[] = [
        // {
        //     title: '序号',
        //     dataIndex: 'index',
        //     valueType: 'index',
        // },
        {
            title: 'Icon',
            dataIndex: 'icon',
            width: 48,
            search: false,
            render: (text: any) => (
                <img src={text} alt="" style={{ width: 30, height: 30 }} />
            ),
        },
        {
            title: '名称',
            dataIndex: 'title',
            width: 150,
            ellipsis: true,
        },
        {
            title: '品牌',
            dataIndex: 'brand',
            ellipsis: true,
            search: false,
            width: 150,
            valueType: 'select',
            valueEnum: getBrandValueEnum(),
        },
        {
            title: "能力",
            dataIndex: "supportFeatures",
            search: false,
            width: 200,
            render: (features: any) => {
                if (!features) {
                    return "";
                }

                return features.map((feature: any) => <Tag key={feature}>{feature}</Tag>);
            },
        },
        {
            title: "描述",
            dataIndex: "description",
            search: false,
            width: "20%",
        },
        {
            title: '操作',
            valueType: 'option',
            key: 'option',
            width: 150,
            render: (_, record, __, action) => [
                <a
                    key="editable"
                    onClick={() => {
                        action?.startEditable?.(record.id);
                    }}
                >
                    编辑
                </a>,
                <a style={{ color: '#ff4d4f' }} key="del">
                    删除
                </a>,
            ],
        },
    ];

    // 查询列表数据的方法
    const fetchTableData = async (params: any, _sort: any, _filter: any) => {
        try {
            // 处理品牌筛选逻辑
            let brandFilter = "";

            // 如果左侧菜单选择了品牌，优先使用菜单选择的品牌
            if (selectBrand && selectBrand !== 'all') { // 'all' 是"全部数据"的key
                brandFilter = selectBrand;
            }

            const { data, errorCode } = await getAiLlmList({
                brand: brandFilter,
                title: params.title || "",
                pageNumber: params.current || 1,
                pageSize: params.pageSize || 10,
            });

            if (errorCode === 0) {
                return {
                    data: data?.records || [],
                    success: true,
                    total: data?.totalRow || 0,
                };
            } else {
                message.error("获取列表失败");
                return {
                    data: [],
                    success: false,
                    total: 0,
                };
            }
        } catch (error) {
            message.error("查询失败");
            return {
                data: [],
                success: false,
                total: 0,
            };
        }
    };

    // 重置查询条件
    const handleReset = () => {
        actionRef.current?.reset?.();
    };

    // 手动触发查询
    const handleSearch = () => {
        actionRef.current?.reload();
    };

    // 监听左侧菜单选择变化，自动刷新表格
    useEffect(() => {
        if (actionRef.current) {
            actionRef.current.reload();
        }
    }, [selectBrand]);

    return (
        <ProTable<TableListItem>
            columns={columns}
            actionRef={actionRef}
            request={fetchTableData}
            rowSelection={{}}
            tableAlertRender={({
                selectedRowKeys,
                selectedRows,
                onCleanSelected,
            }) => {
                console.log(selectedRowKeys, selectedRows);
                return (
                    <Space size={24}>
                        <span>
                            已选 {selectedRowKeys.length} 项
                            <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                                取消选择
                            </a>
                        </span>
                    </Space>
                );
            }}
            tableAlertOptionRender={() => {
                return <a>批量删除</a>
            }}
            rowKey="id"
            search={{
                labelWidth: 'auto',
            }}
            pagination={{
                showSizeChanger: true,
                defaultPageSize: 10,
                pageSizeOptions: ['10', '20', '50', '100'],
            }}
            onSubmit={handleSearch}
            onReset={handleReset}
            headerTitle={
                <Button
                    key="add"
                    icon={<PlusOutlined />}
                    onClick={() => {
                        // 这里可以添加新增逻辑
                        console.log('新增');
                    }}
                    type="primary"
                >
                    新增
                </Button>
            }
        />
    );
};

export default TableList;

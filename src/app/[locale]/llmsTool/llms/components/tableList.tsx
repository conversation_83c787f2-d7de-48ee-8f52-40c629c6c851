'use client';
import { useRef } from 'react';
import { EllipsisOutlined, PlusOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable, TableDropdown } from '@ant-design/pro-components';
import { Button, Dropdown, Space, Tag, Table, message } from 'antd';
import { LLMModelConfig as TableListItem } from "@/types/llms"
import { getAiLlmList } from "@/services/llms/api"
import type { GetProp, MenuProps } from 'antd';
type MenuItem = GetProp<MenuProps, 'items'>[number];

interface IProps {
    modelList: MenuItem[];
    selectBrand: string
}
const TableList: React.FC<IProps> = ({ modelList, selectBrand }) => {
    const actionRef = useRef<ActionType>();

    // 将 modelList 转换为 valueEnum 格式
    const getBrandValueEnum = () => {
        const valueEnum: Record<string, { text: string; status?: string }> = {};

        modelList.forEach((item: any) => {
            const key = item.key;
            const text = item.label || item.title;

            if (key && text) {
                valueEnum[key] = {
                    text: text,
                };
            }
        });

        return valueEnum;
    };

    const columns: ProColumns<TableListItem>[] = [
        {
            dataIndex: 'index',
            width: 48,
            search: false,
        },
        {
            title: 'Icon',
            dataIndex: 'icon',
            width: 48,
            search: false,
            render: (text: any) => (
                <img src={text} alt="" style={{ width: 30, height: 30 }} />
            ),
        },
        {
            title: '名称',
            dataIndex: 'title',
            width: 150,
            ellipsis: true,
        },
        {
            title: '品牌',
            dataIndex: 'brand',
            ellipsis: true,
            width: 150,
            valueType: 'select',
            valueEnum: getBrandValueEnum(),
        },
        {
            title: "能力",
            dataIndex: "supportFeatures",
            search: false,
            width: 200,
            render: (features: any) => {
                if (!features) {
                    return "";
                }

                return features.map((feature: any) => <Tag key={feature}>{feature}</Tag>);
            },
        },
        {
            title: "描述",
            dataIndex: "description",
            search: false,
            width: "20%",
        },
        {
            title: '操作',
            valueType: 'option',
            key: 'option',
            width: 150,
            render: (_, record, __, action) => [
                <a
                    key="editable"
                    onClick={() => {
                        action?.startEditable?.(record.id);
                    }}
                >
                    编辑
                </a>,
                <a style={{ color: '#ff4d4f' }} key="del">
                    删除
                </a>,
            ],
        },
    ];

    // 查询列表数据的方法
    const fetchTableData = async (params: any, _sort: any, _filter: any) => {
        try {
            const { data, errorCode } = await getAiLlmList({
                brand: params.brand || "",
                title: params.title || "",
                pageNumber: params.current || 1,
                pageSize: params.pageSize || 10,
            });

            if (errorCode === 0) {
                return {
                    data: data?.records || [],
                    success: true,
                    total: data?.totalPage || 0,
                };
            } else {
                message.error("获取列表失败");
                return {
                    data: [],
                    success: false,
                    total: 0,
                };
            }
        } catch (error) {
            message.error("查询失败");
            return {
                data: [],
                success: false,
                total: 0,
            };
        }
    };

    // 重置查询条件
    const handleReset = () => {
        actionRef.current?.reset?.();
    };

    // 手动触发查询
    const handleSearch = () => {
        actionRef.current?.reload();
    };

    return (
        <ProTable<TableListItem>
            columns={columns}
            actionRef={actionRef}
            request={fetchTableData}
            rowSelection={{
                selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
                defaultSelectedRowKeys: [],
            }}
            tableAlertRender={({
                selectedRowKeys,
                selectedRows,
                onCleanSelected,
            }) => {
                console.log(selectedRowKeys, selectedRows);
                return (
                    <Space size={24}>
                        <span>
                            已选 {selectedRowKeys.length} 项
                            <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                                取消选择
                            </a>
                        </span>
                    </Space>
                );
            }}
            tableAlertOptionRender={() => {
                return <a>批量删除</a>
            }}
            // columnsState={{
            //     persistenceKey: 'pro-table-singe-demos',
            //     persistenceType: 'localStorage',
            //     defaultValue: {
            //         option: { fixed: 'right', disable: true },
            //     },
            //     onChange(value) {
            //         console.log('value: ', value);
            //     },
            // }}
            rowKey="id"
            search={{
                labelWidth: 'auto',
            }}
            pagination={{
                showSizeChanger: true,
            }}
            // headerTitle="高级表格"
            toolBarRender={() => [
                <Button
                    key="search"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                    type="primary"
                >
                    查询
                </Button>,
                <Button
                    key="reset"
                    icon={<ReloadOutlined />}
                    onClick={handleReset}
                >
                    重置
                </Button>,
                <Button
                    key="add"
                    icon={<PlusOutlined />}
                    onClick={() => {
                        // 这里可以添加新增逻辑
                        console.log('新增');
                    }}
                    type="default"
                >
                    新增
                </Button>
            ]}
        />
    );
};

export default TableList;

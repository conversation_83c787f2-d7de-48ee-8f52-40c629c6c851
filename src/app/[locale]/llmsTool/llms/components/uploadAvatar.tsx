'use client';
import { useState, useEffect } from 'react';
import { Upload, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { GetProp, UploadProps } from 'antd';
import ImgCrop from 'antd-img-crop';
import Cookies from 'js-cookie';

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

interface IProps { }

const UploadAvatar: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();
  const getBase64 = (img: FileType, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(img);
  };

  const beforeUpload = (file: FileType) => {
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('文件大小不能超过 2MB!');
    }
    return isLt2M;
  };

  const handleChange: UploadProps['onChange'] = (info) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      getBase64(info.file.originFileObj as FileType, (url) => {
        setLoading(false);
        setImageUrl(url);
      });
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传</div>
    </div>
  );

  return (
    <ImgCrop rotationSlider>
      <Upload
        name="avatar"
        listType="picture-circle"
        showUploadList={false}
        accept="image/png, image/jpeg, image/jpg, image/bmp, image/webp, image/svg"
        action={process.env.NEXT_PUBLIC_AI_BASE_URL + '/ai-flow/api/v1/commons/uploadPublic'}
        beforeUpload={beforeUpload}
        onChange={handleChange}
        headers={{ Authorization: Cookies.get('access_token') as string }}
      >
        {imageUrl ? <img src={imageUrl} alt="avatar" style={{ width: '100%' }} /> : uploadButton}
      </Upload>
    </ImgCrop>
    <Upload
      name="avatar"
      listType="picture-circle"
      className="avatar-uploader"
      showUploadList={false}
      action={process.env.NEXT_PUBLIC_AI_BASE_URL + '/ai-flow/api/v1/commons/uploadPublic'}
      beforeUpload={beforeUpload}
      onChange={handleChange}
      headers={{ Authorization: Cookies.get('access_token') as string }}
    >
      {imageUrl ? <img src={imageUrl} alt="avatar" style={{ width: '100%' }} /> : uploadButton}
    </Upload>
  );
};

export default UploadAvatar;

'use client';
import { useEffect, useState, useRef } from 'react';
import { Menu } from 'antd';
import type { GetProp, MenuProps } from 'antd';
type MenuItem = GetProp<MenuProps, 'items'>[number];

interface IProps {
    modelList: MenuItem[];
}

const BrandListPage: React.FC<IProps> = ({ modelList }) => {
    return (
        <Menu
            defaultSelectedKeys={['1']}
            defaultOpenKeys={['sub1']}
            mode="inline"
            items={modelList}
        />

    );
};

export default BrandListPage;

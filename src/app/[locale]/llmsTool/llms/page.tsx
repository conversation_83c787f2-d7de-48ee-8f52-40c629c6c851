'use client';
import { useEffect, useState, useRef, CSSProperties, ReactNode } from 'react';
import Layout from '@/components/Layout';
import { Flex, message } from 'antd';
import { getAiLlmModelList } from "@/services/llms/api"
import BrandListPage from "./components/brandList"
import TableList from "./components/tableList"
import * as Icons from '@ant-design/icons';

const outerStyle: CSSProperties = {
  // backgroundColor: 'transparent'
};
const LlmsPage: React.FC = () => {
  const [modelList, setModelList] = useState<any[]>([])

  // 将字符串图标名转换为 ReactNode
  const getIconComponent = (iconName: string): ReactNode => {
    if (!iconName) return null;

    // 动态获取图标组件
    const IconComponent = (Icons as any)[iconName];
    if (IconComponent) {
      return <IconComponent />;
    }

    // 如果找不到对应的图标，返回默认图标或 null
    return <Icons.QuestionCircleOutlined />;
  };

  //查询品牌数据
  const fetchAiLlmModelList = async () => {
    const { data, errorCode } = await getAiLlmModelList();
    data.forEach((item: any) => {
      item.label = item.title;
      // 将字符串图标转换为 ReactNode
      if (item.icon) {
        item.icon = getIconComponent(item.icon);
      }
    });

    if (errorCode === 0) {
      setModelList(data)
    } else {
      message.error("获取品牌数据失败")
    }
  }

  useEffect(() => {
    fetchAiLlmModelList();
  }, [])



  return (
    <Layout curActive={`/llmsTool/llms`} outerStyle={outerStyle}>
      <Flex gap={20}>
        <BrandListPage modelList={modelList} />
        <TableList modelList={modelList} />
      </Flex>
    </Layout >

  );
};

export default LlmsPage;

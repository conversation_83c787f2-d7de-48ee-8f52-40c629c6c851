'use client';
import { useEffect, useState, useRef, CSSProperties } from 'react';
import Layout from '@/components/Layout';
import { Flex, message } from 'antd';
import { getAiLlmModelList } from "@/services/llms/api"
import BrandListPage from "./components/brandList"
import TableList from "./components/tableList"

const outerStyle: CSSProperties = {
  // backgroundColor: 'transparent'
};
const LlmsPage: React.FC = () => {
  const [modelList, setModelList] = useState<any[]>([])

  //查询品牌数据
  const fetchAiLlmModelList = async () => {
    const { data, errorCode } = await getAiLlmModelList();
    data.forEach(item => item.label = item.title)
    if (errorCode === 0) {
      setModelList(data)
    } else {
      message.error("获取品牌数据失败")
    }

  }

  useEffect(() => {
    fetchAiLlmModelList();
  }, [])



  return (
    <Layout curActive={`/llmsTool/llms`} outerStyle={outerStyle}>
      <Flex gap={20}>
        <BrandListPage modelList={modelList} />
        <TableList modelList={modelList} />
      </Flex>
    </Layout >

  );
};

export default LlmsPage;

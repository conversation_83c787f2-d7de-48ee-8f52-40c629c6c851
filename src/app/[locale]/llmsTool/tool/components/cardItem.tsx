'use client';
import React from 'react';
import { Card, Avatar, Typography, Tag, Button } from 'antd';
import { EditOutlined, DeleteOutlined, SettingOutlined } from '@ant-design/icons';
import { ToolItem } from '@/types/tool';
import styles from '../styles.module.css';

const { Text, Paragraph } = Typography;

interface CardItemProps {
    item: ToolItem;
    onEdit?: (item: ToolItem) => void;
    onDelete?: (item: ToolItem) => void;
    onConfig?: (item: ToolItem) => void;
}

const CardItem: React.FC<CardItemProps> = ({ item, onEdit, onDelete, onConfig }) => {
    const handleEdit = () => {
        onEdit?.(item);
    };

    const handleDelete = () => {
        onDelete?.(item);
    };

    const handleConfig = () => {
        onConfig?.(item);
    };

    return (
        <Card
            hoverable
            className={styles.toolCard}
            styles={{
                body: {
                    padding: '24px',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column'
                }
            }}
            actions={[
                <Button
                    key="config"
                    type="text"
                    icon={<SettingOutlined />}
                    onClick={handleConfig}
                    size="small"
                    style={{
                        color: '#1890ff',
                        borderRadius: '6px',
                        fontWeight: 500
                    }}
                >
                    工具
                </Button>,
                <Button
                    key="edit"
                    type="text"
                    icon={<EditOutlined />}
                    onClick={handleEdit}
                    size="small"
                    style={{
                        color: '#52c41a',
                        borderRadius: '6px',
                        fontWeight: 500
                    }}
                >
                    编辑
                </Button>,
                <Button
                    key="delete"
                    type="text"
                    icon={<DeleteOutlined />}
                    onClick={handleDelete}
                    size="small"
                    danger
                    style={{
                        borderRadius: '6px',
                        fontWeight: 500
                    }}
                >
                    删除
                </Button>,
            ]}
        >
            <div className={styles.cardBody}>
                {/* 头部：图标和标题 */}
                <div className={styles.cardHeader}>
                    <Avatar
                        src={item.icon}
                        icon={!item.icon && <SettingOutlined />}
                        size={48}
                        className={styles.cardAvatar}
                        style={{
                            backgroundColor: !item.icon ? '#667eea' : undefined
                        }}
                    />
                    <div style={{ flex: 1, minWidth: 0 }}>
                        <Text
                            strong
                            className={styles.cardTitle}
                            ellipsis
                        >
                            {item.name}
                        </Text>
                        {item.category && (
                            <Tag
                                color="blue"
                                className={styles.cardCategory}
                            >
                                {item.category}
                            </Tag>
                        )}
                    </div>
                </div>

                {/* 描述 */}
                <div style={{ flex: 1, marginBottom: '16px' }}>
                    <Paragraph
                        ellipsis={{ rows: 3, expandable: false }}
                        className={styles.cardDescription}
                        style={{ margin: 0 }}
                    >
                        {item.description || '暂无描述信息...'}
                    </Paragraph>
                </div>

                {/* 底部状态信息 */}
                <div className={styles.cardFooter}>
                    <div>
                        {item.status && (
                            <Tag
                                color={item.status === 'active' ? 'success' : 'default'}
                                className={styles.statusTag}
                            >
                                {item.status === 'active' ? '✓ 启用' : '○ 禁用'}
                            </Tag>
                        )}
                    </div>
                    {item.updateTime && (
                        <Text
                            type="secondary"
                            className={styles.updateTime}
                        >
                            {new Date(item.updateTime).toLocaleDateString()}
                        </Text>
                    )}
                </div>
            </div>
        </Card>
    );
};

export default CardItem;
'use client';
import React from 'react';
import { Card, Avatar, Typography, Space, Tag, Button } from 'antd';
import { EditOutlined, DeleteOutlined, SettingOutlined } from '@ant-design/icons';
import { ToolItem } from '@/types/tool';

const { Text, Paragraph } = Typography;

interface CardItemProps {
    item: ToolItem;
    onEdit?: (item: ToolItem) => void;
    onDelete?: (item: ToolItem) => void;
    onConfig?: (item: ToolItem) => void;
}

const CardItem: React.FC<CardItemProps> = ({ item, onEdit, onDelete, onConfig }) => {
    const handleEdit = () => {
        onEdit?.(item);
    };

    const handleDelete = () => {
        onDelete?.(item);
    };

    const handleConfig = () => {
        onConfig?.(item);
    };

    return (
        <Card
            hoverable
            style={{
                height: '100%',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}
            styles={{
                body: {
                    padding: '20px',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column'
                }
            }}
            actions={[
                <Button
                    key="config"
                    type="text"
                    icon={<SettingOutlined />}
                    onClick={handleConfig}
                    size="small"
                >
                    工具
                </Button>,
                <Button
                    key="edit"
                    type="text"
                    icon={<EditOutlined />}
                    onClick={handleEdit}
                    size="small"
                >
                    编辑
                </Button>,
                <Button
                    key="delete"
                    type="text"
                    icon={<DeleteOutlined />}
                    onClick={handleDelete}
                    size="small"
                    danger
                >
                    删除
                </Button>,
            ]}
        >
            <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                {/* 头部：图标和标题 */}
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                    <Avatar
                        src={item.icon}
                        icon={!item.icon && <SettingOutlined />}
                        size={40}
                        style={{
                            backgroundColor: !item.icon ? '#f0f0f0' : undefined,
                            marginRight: '12px',
                            flexShrink: 0
                        }}
                    />
                    <div style={{ flex: 1, minWidth: 0 }}>
                        <Text strong style={{ fontSize: '16px', display: 'block' }} ellipsis>
                            {item.name}
                        </Text>
                        {item.category && (
                            <Tag color="blue" style={{ marginTop: '4px', fontSize: '12px' }}>
                                {item.category}
                            </Tag>
                        )}
                    </div>
                </div>

                {/* 描述 */}
                <div style={{ flex: 1, marginBottom: '12px' }}>
                    <Paragraph
                        ellipsis={{ rows: 3, expandable: false }}
                        style={{
                            margin: 0,
                            color: '#666',
                            fontSize: '14px',
                            lineHeight: '1.5'
                        }}
                    >
                        {item.description || '暂无描述'}
                    </Paragraph>
                </div>

                {/* 底部状态信息 */}
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Space>
                        {item.status && (
                            <Tag color={item.status === 'active' ? 'green' : 'default'} style={{ fontSize: '12px' }}>
                                {item.status === 'active' ? '启用' : '禁用'}
                            </Tag>
                        )}
                    </Space>
                    {item.updateTime && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                            {new Date(item.updateTime).toLocaleDateString()}
                        </Text>
                    )}
                </div>
            </div>
        </Card>
    );
};

export default CardItem;
'use client';
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Upload,
  Button,
  Select,
  Radio,
  message,
  Row,
  Col
} from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { ToolPluginUpdate } from '@/types/tool';
import { save, update } from '@/services/tool/api';
import Cookies from 'js-cookie';

const { TextArea } = Input;
const { Option } = Select;

interface AddModalProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  editData?: ToolPluginUpdate | null;
  isEdit?: boolean;
}

const AddModal: React.FC<AddModalProps> = ({
  open,
  onCancel,
  onSuccess,
  editData,
  isEdit = false
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>('');

  // 当弹窗打开时，如果是编辑模式，填充表单数据
  useEffect(() => {
    if (open) {
      console.log(editData);

      if (isEdit && editData) {
        form.setFieldsValue({
          ...editData,
          headers: editData.headers ? JSON.parse(editData.headers) : []
        });
        setImageUrl(editData.icon || '');
      } else {
        form.resetFields();
        setImageUrl('');
        // 设置默认值
        form.setFieldsValue({
          authType: '无需认证',
          position: 'headers',
          headers: []
        });
      }
    }
  }, [open, isEdit, editData, form]);

  // 处理图片上传
  const handleUploadChange = (info: any) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      const url = info.file.response?.data?.url;
      if (url) {
        setImageUrl(url);
        form.setFieldValue('icon', url);
        message.success('图片上传成功');
      }
    } else if (info.file.status === 'error') {
      message.error('图片上传失败');
    }
  };

  // 上传前的校验
  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB!');
      return false;
    }
    return true;
  };

  // 提交表单
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 处理headers数据，过滤空值
      const headers = (values.headers || []).filter((item: any) =>
        item && item.label && item.value
      ).map((item: any) => ({
        label: item.label,
        value: item.value
      }));

      const submitData = {
        ...values,
        headers,
        icon: imageUrl || values.icon || ''
      };

      const result = isEdit && editData
        ? await update({ ...submitData, id: editData.id })
        : await save(submitData);

      if (result.errorCode === 0) {
        message.success(isEdit ? '编辑成功' : '新增成功');
        onSuccess();
        onCancel();
      } else {
        message.error(result.errorMessage || (isEdit ? '编辑失败' : '新增失败'));
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败');
    } finally {
      setLoading(false);
    }
  };

  // 上传按钮
  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传</div>
    </div>
  );

  return (
    <Modal
      title={
        <div style={{
          fontSize: '18px',
          fontWeight: 600,
          color: '#262626',
          padding: '8px 0'
        }}>
          {isEdit ? '✏️ 编辑插件' : '➕ 新增插件'}
        </div>
      }
      open={open}
      onOk={handleOk}
      onCancel={onCancel}
      width={900}
      okText="确定"
      cancelText="取消"
      confirmLoading={loading}
      styles={{
        body: {
          maxHeight: '75vh',
          overflowY: 'auto',
          padding: '32px'
        },
        header: {
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: '16px'
        }
      }}
      okButtonProps={{
        style: {
          background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
          border: 'none',
          borderRadius: '6px',
          height: '40px',
          fontSize: '14px',
          fontWeight: 500
        }
      }}
      cancelButtonProps={{
        style: {
          borderRadius: '6px',
          height: '40px',
          fontSize: '14px'
        }
      }}
    >
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 19 }}
        style={{ marginTop: '24px' }}
        requiredMark={false}
      >
        {/* 图标上传 */}
        <Form.Item label="插件图标" name="icon">
          <Upload
            name="file"
            listType="picture-card"
            showUploadList={false}
            accept="image/*"
            action={process.env.NEXT_PUBLIC_AI_BASE_URL + "/ai-flow/api/v1/commons/upload"}
            beforeUpload={beforeUpload}
            onChange={handleUploadChange}
            headers={{ Authorization: Cookies.get('access_token') as string }}
          >
            {imageUrl ? (
              <img src={imageUrl} alt="icon" style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
            ) : (
              uploadButton
            )}
          </Upload>
        </Form.Item>

        {/* 插件名称 */}
        <Form.Item
          label="插件名称"
          name="name"
          rules={[{ required: true, message: '请输入插件名称' }]}
          style={{ marginBottom: '24px' }}
        >
          <Input
            placeholder="请输入插件名称"
            maxLength={30}
            showCount
            style={{
              borderRadius: '8px',
              fontSize: '14px',
              height: '40px'
            }}
          />
        </Form.Item>

        {/* 插件描述 */}
        <Form.Item
          label="插件描述"
          name="description"
          rules={[{ required: true, message: '请输入插件描述' }]}
          style={{ marginBottom: '24px' }}
        >
          <TextArea
            placeholder="请输入插件描述，详细说明插件的功能和用途..."
            rows={4}
            maxLength={500}
            showCount
            style={{
              borderRadius: '8px',
              fontSize: '14px'
            }}
          />
        </Form.Item>

        {/* 插件URL */}
        <Form.Item
          label="插件 URL"
          name="baseUrl"
          rules={[
            { required: true, message: '请输入插件 URL' },
            { type: 'url', message: '请输入有效的URL地址' }
          ]}
          style={{ marginBottom: '24px' }}
        >
          <Input
            placeholder="https://example.com/api"
            style={{
              borderRadius: '8px',
              fontSize: '14px',
              height: '40px'
            }}
          />
        </Form.Item>

        {/* Headers */}
        <Form.Item label="Headers">
          <Form.List name="headers">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row key={key} gutter={8} style={{ marginBottom: 8 }}>
                    <Col span={10}>
                      <Form.Item
                        {...restField}
                        name={[name, 'label']}
                        rules={[{ required: true, message: '请输入Headers Name' }]}
                      >
                        <Input placeholder="Headers Name" />
                      </Form.Item>
                    </Col>
                    <Col span={10}>
                      <Form.Item
                        {...restField}
                        name={[name, 'value']}
                        rules={[{ required: true, message: '请输入Headers value' }]}
                      >
                        <Input placeholder="Headers value" />
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Button
                        type="text"
                        icon={<MinusCircleOutlined />}
                        onClick={() => remove(name)}
                        danger
                      />
                    </Col>
                  </Row>
                ))}
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<PlusOutlined />}
                  style={{ marginBottom: 16 }}
                >
                  添加 headers
                </Button>
              </>
            )}
          </Form.List>
        </Form.Item>

        {/* 认证方式 */}
        <Form.Item
          label="认证方式"
          name="authType"
          rules={[{ required: true, message: '请选择认证方式' }]}
        >
          <Select placeholder="请选择认证方式">
            <Option value="none">无需认证</Option>
            <Option value="apiKey">Service token / API key</Option>
          </Select>
        </Form.Item>

        {/* 当选择Service token时显示额外字段 */}
        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
          prevValues.authType !== currentValues.authType
        }>
          {({ getFieldValue }) => {
            const authType = getFieldValue('authType');
            return authType === 'apiKey' ? (
              <>
                {/* 参数位置 */}
                <Form.Item
                  label="参数位置"
                  name="position"
                  rules={[{ required: true, message: '请选择参数位置' }]}
                >
                  <Radio.Group>
                    <Radio value="headers">headers</Radio>
                    <Radio value="query">query</Radio>
                  </Radio.Group>
                </Form.Item>

                {/* tokenKey */}
                <Form.Item
                  label="tokenKey"
                  name="tokenKey"
                  rules={[{ required: true, message: '请输入tokenKey' }]}
                >
                  <TextArea
                    placeholder="请输入tokenKey"
                    rows={3}
                    maxLength={500}
                    showCount
                  />
                </Form.Item>

                {/* tokenValue */}
                <Form.Item
                  label="tokenValue"
                  name="tokenValue"
                  rules={[{ required: true, message: '请输入tokenValue' }]}
                >
                  <TextArea
                    placeholder="请输入tokenValue"
                    rows={3}
                    maxLength={2000}
                    showCount
                  />
                </Form.Item>
              </>
            ) : null;
          }}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddModal;

'use client';
import React, { useEffect, useState } from 'react';
import Layout from '@/components/Layout';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { Card, Input, Space, Button, message, Row, Col, Spin, Pagination, Empty } from 'antd';
import { aiPluginList } from '@/services/tool/api';
import { ToolItem } from '@/types/tool';
import CardItem from './components/cardItem';

const ToolPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchName, setSearchName] = useState('');
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(12);
  const [toolList, setToolList] = useState<ToolItem[]>([]);

  // 获取工具列表数据
  const fetchToolList = async (params?: { name?: string; current?: number; pageSize?: number }) => {
    try {
      setLoading(true);
      const { data, errorCode } = await aiPluginList({
        name: params?.name || searchName || '',
        pageNumber: params?.current || current,
        pageSize: params?.pageSize || pageSize,
      });

      if (errorCode === 0) {
        setToolList(data?.records || []);
        setTotal(data?.totalRow || 0);
        if (params?.current) {
          setCurrent(params.current);
        }
      } else {
        message.error('获取工具列表失败');
      }
    } catch (error) {
      console.error('获取工具列表失败:', error);
      message.error('获取工具列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    fetchToolList();
  }, []);

  const handleSearch = () => {
    setCurrent(1);
    fetchToolList({ name: searchName, current: 1, pageSize });
  };

  const handleReset = () => {
    setSearchName('');
    setCurrent(1);
    fetchToolList({ name: '', current: 1, pageSize });
  };

  const handleAdd = () => {
    message.info('新增插件功能开发中...');
  };

  const handleEdit = (item: ToolItem) => {
    message.info(`编辑插件: ${item.name}`);
  };

  const handleDelete = (item: ToolItem) => {
    message.info(`删除插件: ${item.name}`);
  };

  const handleConfig = (item: ToolItem) => {
    message.info(`配置插件: ${item.name}`);
  };

  const handlePageChange = (page: number, size?: number) => {
    const newPageSize = size || pageSize;
    setCurrent(page);
    if (size && size !== pageSize) {
      setPageSize(newPageSize);
    }
    fetchToolList({ name: searchName, current: page, pageSize: newPageSize });
  };

  return (
    <Layout curActive={`/llmsTool/tool`}>
      <Card variant="borderless">
        {/* 搜索和操作区域 */}
        <div style={{ marginBottom: 24 }}>
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              新增插件
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>
              重置
            </Button>
            <Input.Search
              placeholder="请输入插件名称"
              style={{ width: 260 }}
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              onSearch={handleSearch}
              allowClear
            />
          </Space>
        </div>

        {/* 工具卡片列表 */}
        <Spin spinning={loading}>
          {toolList.length > 0 ? (
            <>
              <Row gutter={[16, 16]}>
                {toolList.map((item) => (
                  <Col key={item.id} xs={24} sm={12} md={8} lg={6} xl={6}>
                    <CardItem
                      item={item}
                      onEdit={handleEdit}
                      onDelete={handleDelete}
                      onConfig={handleConfig}
                    />
                  </Col>
                ))}
              </Row>

              {/* 分页 */}
              {total > pageSize && (
                <div style={{ textAlign: 'center', marginTop: 32 }}>
                  <Pagination
                    current={current}
                    total={total}
                    pageSize={pageSize}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`}
                    onChange={handlePageChange}
                    onShowSizeChange={handlePageChange}
                    pageSizeOptions={['12', '24', '48', '96']}
                  />
                </div>
              )}
            </>
          ) : (
            <Empty
              description="暂无插件数据"
              style={{ margin: '60px 0' }}
            />
          )}
        </Spin>
      </Card>
    </Layout>
  );
};

export default ToolPage;
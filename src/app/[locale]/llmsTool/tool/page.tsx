'use client';
import React, { useEffect, useState } from 'react';
import Layout from '@/components/Layout';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { Input, Button, message, Row, Col, Spin, Pagination, Empty, Modal } from 'antd';
import { aiPluginList, remove } from '@/services/tool/api';
import { ToolItem, ToolPluginUpdate } from '@/types/tool';
import CardItem from './components/cardItem';
import AddModal from './components/addModal';
import styles from './styles.module.css';

const ToolPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchName, setSearchName] = useState('');
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(12);
  const [toolList, setToolList] = useState<ToolItem[]>([]);

  // 弹窗状态管理
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [editData, setEditData] = useState<ToolPluginUpdate | null>(null);
  const [isEdit, setIsEdit] = useState(false);

  // 获取工具列表数据
  const fetchToolList = async (params?: { name?: string; current?: number; pageSize?: number }) => {
    try {
      setLoading(true);
      const { data, errorCode } = await aiPluginList({
        name: params?.name || searchName || '',
        pageNumber: params?.current || current,
        pageSize: params?.pageSize || pageSize,
      });

      if (errorCode === 0) {
        setToolList(data?.records || []);
        setTotal(data?.totalRow || 0);
        if (params?.current) {
          setCurrent(params.current);
        }
      } else {
        message.error('获取工具列表失败');
      }
    } catch (error) {
      console.error('获取工具列表失败:', error);
      message.error('获取工具列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    fetchToolList();
  }, []);

  const handleSearch = () => {
    setCurrent(1);
    fetchToolList({ name: searchName, current: 1, pageSize });
  };

  const handleReset = () => {
    setSearchName('');
    setCurrent(1);
    fetchToolList({ name: '', current: 1, pageSize });
  };

  const handleAdd = () => {
    setIsEdit(false);
    setEditData(null);
    setAddModalOpen(true);
  };

  const handleEdit = (item: ToolItem) => {
    setIsEdit(true);
    setEditData(item as ToolPluginUpdate);
    setAddModalOpen(true);
  };

  const handleDelete = (item: ToolItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除插件"${item.name}"吗？此操作不可逆，请谨慎操作。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const { errorCode } = await remove(item.id);
          if (errorCode === 0) {
            message.success('删除成功');
            // 如果当前页没有数据了，回到上一页
            if (toolList.length === 1 && current > 1) {
              setCurrent(current - 1);
              fetchToolList({ name: searchName, current: current - 1, pageSize });
            } else {
              fetchToolList({ name: searchName, current, pageSize });
            }
          } else {
            message.error('删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      }
    });
  };

  const handleConfig = (item: ToolItem) => {
    message.info(`配置插件: ${item.name}`);
  };

  // 弹窗成功回调
  const handleModalSuccess = () => {
    fetchToolList({ name: searchName, current, pageSize });
  };

  // 关闭弹窗
  const handleModalCancel = () => {
    setAddModalOpen(false);
    setEditData(null);
    setIsEdit(false);
  };

  const handlePageChange = (page: number, size?: number) => {
    const newPageSize = size || pageSize;
    setCurrent(page);
    if (size && size !== pageSize) {
      setPageSize(newPageSize);
    }
    fetchToolList({ name: searchName, current: page, pageSize: newPageSize });
  };

  return (
    <Layout curActive={`/llmsTool/tool`}>
      <div className={styles.toolPage}>
        {/* 页面标题区域 */}
        <div className={styles.pageHeader}>
          <div className={styles.headerContent}>
            <div>
              <h2 className={styles.pageTitle}>
                插件管理
              </h2>
              <p className={styles.pageSubtitle}>
                管理和配置您的AI插件工具
              </p>
            </div>

            <div className={styles.actionButtons}>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleReset}
                className={styles.secondaryButton}
              >
                重置
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
                className={styles.primaryButton}
              >
                新增插件
              </Button>
            </div>
          </div>

          {/* 搜索区域 */}
          <div className={styles.searchSection}>
            <Input.Search
              placeholder="搜索插件名称..."
              className={styles.searchInput}
              style={{ width: '320px' }}
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              onSearch={handleSearch}
              allowClear
              size="large"
            />
          </div>
        </div>

        {/* 工具卡片列表 */}
        <div className={styles.contentArea}>
          <Spin spinning={loading}>
            {toolList.length > 0 ? (
              <>
                <Row gutter={[20, 20]} className={styles.cardGrid}>
                  {toolList.map((item) => (
                    <Col key={item.id} xs={24} sm={12} md={8} lg={6} xl={6}>
                      <CardItem
                        item={item}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                        onConfig={handleConfig}
                      />
                    </Col>
                  ))}
                </Row>

                {/* 分页 */}
                {total > pageSize && (
                  <div className={styles.paginationWrapper}>
                    <Pagination
                      current={current}
                      total={total}
                      pageSize={pageSize}
                      showSizeChanger
                      showQuickJumper
                      showTotal={(total, range) =>
                        <span style={{ color: '#8c8c8c' }}>
                          第 {range[0]}-{range[1]} 条/共 {total} 条
                        </span>
                      }
                      onChange={handlePageChange}
                      onShowSizeChange={handlePageChange}
                      pageSizeOptions={['12', '24', '48', '96']}
                    />
                  </div>
                )}
              </>
            ) : (
              <div className={styles.emptyState}>
                <Empty
                  description={
                    <span className={styles.emptyDescription}>
                      {searchName ? '未找到相关插件' : '暂无插件数据'}
                    </span>
                  }
                  style={{ margin: 0 }}
                />
                {!searchName && (
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAdd}
                    className={styles.emptyButton}
                  >
                    立即添加插件
                  </Button>
                )}
              </div>
            )}
          </Spin>
        </div>
      </div>

      {/* 新增/编辑弹窗 */}
      <AddModal
        open={addModalOpen}
        onCancel={handleModalCancel}
        onSuccess={handleModalSuccess}
        editData={editData}
        isEdit={isEdit}
      />
    </Layout>
  );
};

export default ToolPage;
'use client';
import React, { useEffect, useState } from 'react';
import Layout from '@/components/Layout';
import { PlusOutlined } from '@ant-design/icons';
import { Card, Input, Space, Button, message, Form, Row, Col } from 'antd';


const ToolPage: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [searchName, setSearchName] = useState('');
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(35);
  const handleSearch = (values: any) => {

  }

  const resetSearch = () => {

  }

  const handleAdd = () => {

  }

  return (
    <Layout curActive={`/llmsTool/tool`}>
      <Card variant="borderless">
        <div style={{ marginBottom: 24 }}>
          <Space>
            {/* {roleJudgment(userInfo, 'PROMPT_ADD') && ( */}
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              新增插件
            </Button>
            {/* )} */}
            <Input.Search
              placeholder="请输入模板名称"
              style={{ width: 260 }}
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              onSearch={() => handleSearch({ params: { name: searchName }, current, pageSize })}
              allowClear
            />
          </Space>
        </div>
      </Card>
    </Layout >)
};

export default ToolPage;
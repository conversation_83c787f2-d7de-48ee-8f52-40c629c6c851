/* Tool Page Styles */
.toolPage {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 24px;
}

.pageHeader {
  margin-bottom: 24px;
  background: #fff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.pageTitle {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.pageSubtitle {
  margin: 8px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 400;
}

.actionButtons {
  display: flex;
  gap: 12px;
}

.primaryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  height: 40px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
}

.secondaryButton {
  border-radius: 8px;
  height: 40px;
  font-weight: 500;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.secondaryButton:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.searchSection {
  margin-top: 20px;
}

.searchInput {
  border-radius: 8px;
  height: 44px;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.searchInput:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.contentArea {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  min-height: 400px;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.cardGrid {
  margin-bottom: 32px;
}

.toolCard {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
}

.toolCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.cardBody {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f5f5f5;
}

.cardAvatar {
  margin-right: 16px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cardTitle {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
  margin: 0;
}

.cardCategory {
  margin-top: 6px;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
}

.cardDescription {
  flex: 1;
  margin-bottom: 16px;
  color: #595959;
  font-size: 14px;
  line-height: 1.6;
  min-height: 66px;
}

.cardFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f5f5f5;
}

.statusTag {
  border-radius: 12px;
  padding: 2px 8px;
  font-weight: 500;
  font-size: 12px;
}

.updateTime {
  font-size: 12px;
  color: #8c8c8c;
}

.paginationWrapper {
  text-align: center;
  margin-top: 40px;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.emptyDescription {
  color: #8c8c8c;
  font-size: 16px;
  margin-bottom: 16px;
}

.emptyButton {
  border-radius: 8px;
  height: 40px;
  font-weight: 500;
}

/* Modal Styles */
.modalTitle {
  font-size: 20px;
  font-weight: 700;
  color: #262626;
  padding: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modalBody {
  max-height: 75vh;
  overflow-y: auto;
  padding: 32px;
}

.formItem {
  margin-bottom: 24px;
}

.formInput {
  border-radius: 8px;
  font-size: 14px;
  height: 40px;
  transition: all 0.3s ease;
}

.formInput:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.formTextarea {
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.formTextarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.uploadArea {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.uploadArea:hover {
  border-color: #667eea;
  background: #f0f2ff;
}

.headerRow {
  margin-bottom: 8px;
}

.addHeaderButton {
  margin-bottom: 16px;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  height: 40px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.addHeaderButton:hover {
  border-color: #667eea;
  color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
  .toolPage {
    padding: 16px;
  }
  
  .pageHeader {
    padding: 20px;
  }
  
  .headerContent {
    flex-direction: column;
    align-items: stretch;
  }
  
  .actionButtons {
    justify-content: center;
  }
  
  .contentArea {
    padding: 20px;
  }
}

@media (max-width: 576px) {
  .pageTitle {
    font-size: 20px;
  }
  
  .searchInput {
    width: 100% !important;
  }
  
  .modalBody {
    padding: 24px 20px;
  }
}

'use client';
import { useRef, useState } from 'react';
import { PlusOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm } from 'antd';
import { removeTool, getToolList } from '@/services/tool/api';
import AddToolModal from '../components/addToolModal';
import { roleJudgment } from '@/utils/index';
import useStore from '@/store/useStore';
import { useSearchParams, useRouter } from 'next/navigation';
import Layout from '@/components/Layout';
import dayjs from 'dayjs';

interface ToolListItem {
    name: string;
    description: string;
    id: string;
    created: number;
}

const ToolPage: React.FC = () => {
    const actionRef = useRef<ActionType>();
    const { userInfo } = useStore();
    const searchParams = useSearchParams();
    const router = useRouter();
    // 获取查询参数
    const id = searchParams.get('id');
    const [addModalOpen, setAddModalOpen] = useState(false);

    // 返回上一页
    const handleGoBack = () => {
        router.push('/llmsTool/tool');
    };

    const columns: ProColumns<ToolListItem>[] = [
        {
            title: '工具名称',
            dataIndex: 'name',
            width: 150,
            ellipsis: true,
        },
        {
            title: '工具描述',
            dataIndex: 'description',
            ellipsis: true,
            search: false,
            width: 200,
        },
        {
            title: '创建时间',
            dataIndex: 'created',
            search: false,
            width: 200,
            render: (_, record) => (record.created ? dayjs(record.created).format('YYYY-MM-DD HH:mm:ss') : '-'),
        },
        {
            title: '操作',
            valueType: 'option',
            key: 'option',
            width: 150,
            render: (_, record) => [
                <>
                    {roleJudgment(userInfo, 'LLMS_EDIT') && (
                        <a key="edit" onClick={() => handleEdit(record)}>
                            修改
                        </a>
                    )}
                </>,
                <>
                    {roleJudgment(userInfo, 'LLMS_DELETE') && (
                        <Popconfirm
                            title="确认删除？"
                            description="您确定要删除这条数据吗？"
                            onConfirm={() => handleDelete(record.id)}
                            okText="确认"
                            cancelText="取消"
                        >
                            <a style={{ color: '#ff4d4f' }} key="del">
                                删除
                            </a>
                        </Popconfirm>
                    )}
                </>,
            ],
        },
    ];

    // 刷新表格（删除后自动处理分页）
    const smartReload = () => {
        actionRef.current?.reload();
    };

    // 单个删除
    const handleDelete = async (id: string) => {
        try {
            const { errorCode } = await removeTool(id);
            if (errorCode === 0) {
                message.success('删除成功');
                // 刷新表格
                smartReload();
            } else {
                message.error('删除失败');
            }
        } catch (error) {
            message.error('删除失败');
        }
    };

    // 查询列表数据的方法
    const fetchTableData = async (params: any) => {
        try {
            let currentPage = params.current || 1;
            const pageSize = params.pageSize || 10;

            const { data, errorCode } = await getToolList({
                name: params.name || '',
                pageNumber: currentPage,
                pageSize: pageSize,
                pluginId: id || '',
            });

            if (errorCode === 0) {
                const records = data?.records || [];
                const total = data?.totalRow || 0;

                // 如果当前页没有数据且当前页大于1，则查询前一页
                if (records.length === 0 && currentPage > 1 && total > 0) {
                    const maxPage = Math.ceil(total / pageSize);
                    const targetPage = Math.min(currentPage - 1, maxPage);

                    if (targetPage > 0) {
                        // 递归查询前一页
                        return await fetchTableData({
                            ...params,
                            current: targetPage,
                        });
                    }
                }

                return {
                    data: records,
                    success: true,
                    total: total,
                    current: currentPage,
                };
            } else {
                message.error('获取列表失败');
                return {
                    data: [],
                    success: false,
                    total: 0,
                };
            }
        } catch (error) {
            message.error('查询失败');
            return {
                data: [],
                success: false,
                total: 0,
            };
        }
    };

    // 重置查询条件
    const handleReset = () => {
        actionRef.current?.reset?.();
    };

    // 手动触发查询
    const handleSearch = () => {
        smartReload();
    };

    // 打开新增弹窗
    const handleAdd = () => {
        setAddModalOpen(true);
    };

    // 关闭弹窗
    const handleAddCancel = () => {
        setAddModalOpen(false);
    };

    // 处理提交（新增）
    const handleAddSubmit = () => {
        // 刷新表格
        smartReload();
    };

    //修改
    const handleEdit = (record: ToolListItem) => { };

    return (
        <Layout curActive={`/llmsTool/tool`}>
            <ProTable<ToolListItem>
                columns={columns}
                actionRef={actionRef}
                request={fetchTableData}
                // rowSelection={null}
                tableAlertRender={() => {
                    return <div>123</div>;
                }}
                tableAlertOptionRender={() => {
                    return <div>123</div>;
                }}
                rowKey="id"
                search={{
                    labelWidth: 'auto',
                    optionRender: (searchConfig, formProps, dom) => {
                        const [resetBtn, queryBtn] = dom;
                        return [
                            <Button
                                key="back"
                                icon={<ArrowLeftOutlined />}
                                onClick={handleGoBack}
                                style={{ marginRight: 8 }}
                            >
                                返回
                            </Button>,
                            queryBtn,
                            resetBtn
                        ];
                    },
                }}
                pagination={{
                    showSizeChanger: true,
                    defaultPageSize: 10,
                    pageSizeOptions: ['10', '20', '50', '100'],
                }}
                onSubmit={handleSearch}
                onReset={handleReset}
                headerTitle={
                    <>
                        {roleJudgment(userInfo, 'LLMS_ADD') && (
                            <Button key="add" icon={<PlusOutlined />} onClick={handleAdd} type="primary">
                                创建工具
                            </Button>
                        )}
                    </>
                }
            />

            {/* 新增/编辑弹窗 */}
            <AddToolModal open={addModalOpen} onCancel={handleAddCancel} onSuccess={handleAddSubmit} id={id || ''} />
        </Layout>
    );
};

export default ToolPage;

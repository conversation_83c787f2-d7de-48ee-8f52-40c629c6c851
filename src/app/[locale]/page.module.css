.home {
    padding-top: 160px;
    text-align: center;
    background: rgba(0,0,0,.05) url('/bg.svg');
    background-size: cover;
    width: 100vw;
    height: 100vh;
}

.content {
    width: 60%;
    margin: 30px auto;
    padding: 20px 20px 40px;
    border-radius: 6px;
    background-color: #fff;
}

.content p {
    margin-bottom: 30px;
}

.timeBox {
    display: inline-block;
    width: 40%;
}

@media screen and (max-width: 600px) {
    /* CSS rules */
    .content {
        width: 90%;
        margin: 30px auto;
        padding: 10px 10px 20px;
    }
    .home {
        padding-top: 60px;
    }
    .timeBox {
        width: 60%;
    }
}
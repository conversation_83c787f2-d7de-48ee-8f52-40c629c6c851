'use client';
import React, { useState, useEffect } from 'react';
import { Form, Input, Checkbox, Button, message, Spin, Skeleton } from 'antd';
import { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';
import Cookies from 'js-cookie';
import { useRouter } from 'next/navigation';
import useStore from '@/store/useStore';
import { login, getImageCode } from '@/services/user/api';
import { DecryptByAES, EncryptByAES } from '@/utils/crypto';
import Image from 'next/image';
import styles from './page.module.less';

interface FormParams {
  account: string;
  password: string;
  verifyCode: string;
  remember: boolean;
}

interface ImageCode {
  img: string;
  codeId: string;
}

const Login: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [imgCodeObj, setImgCodeObj] = useState<ImageCode>();
  const { getUserInfo, getMenuList } = useStore();
  const router = useRouter();
  const [form] = Form.useForm<FormParams>();

  const fetchImgCode = async () => {
    const respData = await getImageCode();
    if (respData.success) setImgCodeObj(respData.resp[0]);
  };
  const onFinish = async () => {
    form.validateFields().then(async (values) => {
      setLoading(true);
      const password = EncryptByAES(values.password, 'gREQn9Itz2fSQDX1', 'VFlYxEt3ipaHbkXM');
      const respData = await login({
        ...values,
        password,
        codeId: imgCodeObj?.codeId!,
        keiv: 'VFlYxEt3ipaHbkXM',
        kek: 'gREQn9Itz2fSQDX1',
      });

      if (respData.success) {
        const Dt = respData.resp[0];
        //记住密码功能
        if (values.remember) {
          localStorage.setItem('remember', JSON.stringify({ account: values.account, password }));
        } else {
          localStorage.removeItem('remember');
        }
        Cookies.set('access_token', Dt.accessToken);
        message.success('登录成功');
        //获取个人信息
        // getUserInfo(() => router.push('/agent'));
        getUserInfo(() => router.push('/prompt/list'));
        //查询个人菜单树
        getMenuList();
      } else {
        message.error(respData.msg);
        fetchImgCode();
      }
      setLoading(false);
    });
  };

  // 自动填充账号密码
  const setFormData = () => {
    const rememberData = localStorage.getItem('remember');
    if (rememberData) {
      const data = JSON.parse(rememberData);
      data.password = DecryptByAES(data.password, 'gREQn9Itz2fSQDX1', 'VFlYxEt3ipaHbkXM');
      form.setFieldsValue({ ...data, remember: true });
    }
  };

  useEffect(() => {
    setFormData();
    fetchImgCode();
    const timer = setInterval(() => {
      fetchImgCode();
    }, 60000);

    return () => {
      clearInterval(timer);
    };
  }, []);
  return (
    <Form form={form} initialValues={{ remember: true }} onFinish={onFinish}>
      <Spin spinning={loading}>
        <Form.Item
          name="account"
          rules={[
            {
              required: true,
              message: '请输入工号！',
            },
          ]}
        >
          <Input size="large" prefix={<UserOutlined style={{ color: '#1890FF' }} />} placeholder="请输入工号" />
        </Form.Item>

        <Form.Item
          name="password"
          rules={[
            {
              required: true,
              message: '请输入密码！',
            },
          ]}
        >
          <Input.Password
            size="large"
            prefix={<LockOutlined style={{ color: '#1890FF' }} />}
            placeholder="请输入密码"
          />
        </Form.Item>
        <div className={styles.verifyCode}>
          <Form.Item
            name="verifyCode"
            rules={[
              {
                required: true,
                message: '请输入验证码！',
              },
            ]}
          >
            <Input size="large" prefix={<SafetyOutlined style={{ color: '#1890FF' }} />} placeholder="验证码" />
          </Form.Item>
          <div className={styles.imgCode}>
            {imgCodeObj ? (
              <Image
                src={`data:image/png;base64,${imgCodeObj.img}`}
                width={120}
                height={40}
                alt="点击刷新验证码"
                onClick={fetchImgCode}
              />
            ) : (
              <Skeleton.Image active={true} style={{ width: 120, height: 40 }} />
            )}
          </div>
        </div>
        <div className="login_bts">
          <Form.Item name="remember" valuePropName="checked">
            <Checkbox className="remember">记住密码</Checkbox>
          </Form.Item>
        </div>

        <Form.Item style={{ marginBottom: 5 }}>
          <Button type="primary" htmlType="submit" className="submit" size="large" style={{ width: '100%' }}>
            登录
          </Button>
        </Form.Item>
      </Spin>
    </Form>
  );
};

export default Login;

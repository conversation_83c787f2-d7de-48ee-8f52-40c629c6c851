'use client';
import React, { useState, useRef } from 'react';
import { Card, Input, Space, Button, Popconfirm, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import Layout from '@/components/Layout';
import PublicTable from '@/components/PublicTable';
import { useRouter } from 'next/navigation';
import { deleteRole } from '@/services/role/api';
import { roleJudgment } from '@/utils/index';
import useStore from '@/store/useStore';

interface ListTemplate {
  id: number;
  roleName: string;
  description: string;
  active: number;
  roleCode: string;
}

const RolePage: React.FC = () => {
  const router = useRouter();
  const { userInfo } = useStore();
  const [roleName, setRoleName] = useState<string>('');
  const [defaultQuery, setDefaultQuery] = useState({});
  const publicTableRef = useRef<{
    getTableData: () => void;
    resetCurrentAndPageSize: () => void;
  }>(null);

  const columns: ColumnsType<ListTemplate> = [
    {
      title: '角色名称',
      dataIndex: 'roleName',
      key: 'roleName',
      width: 150,
    },
    {
      title: '角色描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'active',
      key: 'active',
      width: 120,
      render: (_, record) => (record.active ? '启用' : '禁用'),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 200,
      render: (_, record) => (
        <Space size="middle">
          {roleJudgment(userInfo, 'ROLE_EDIT') && <a onClick={() => handleEdit(record.id)}>编辑</a>}
          {roleJudgment(userInfo, 'ROLE_ASSIGN') && record.roleCode != 'def' && (
            <a onClick={() => handleAssign(record.id)}>人员分配</a>
          )}
          {roleJudgment(userInfo, 'ROLE_DELETE') && record.roleCode != 'def' && (
            <Popconfirm
              title="提示"
              description="确定要删除这条数据？"
              onConfirm={() => confirm(record.id as number)}
              okText="确认"
              cancelText="取消"
            >
              <a style={{ color: '#ff4d4f' }}>删除</a>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  //删除角色
  const confirm = async (id: number) => {
    const { success, msg } = await deleteRole(id);
    if (success) {
      message.success('删除成功');
      publicTableRef.current?.getTableData();
    } else {
      message.error(msg || '删除失败');
    }
  };

  //编辑角色
  const handleEdit = (id: number) => {
    router.push(`/system/role/edit/${id}`);
  };

  //分配人员
  const handleAssign = (id: number) => {
    router.push(`/system/role/assign/${id}`);
  };

  const handleSearch = () => {
    setDefaultQuery({ roleName });
  };

  //重置列表
  const resetSearch = () => {
    setRoleName('');
    setDefaultQuery({});
  };

  return (
    <Layout curActive={`/system/role`}>
      <div>
        <Card variant="borderless">
          <div
            style={{
              marginBottom: 24,
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Space>
              <div>角色名称:</div>
              <Input
                value={roleName}
                placeholder="请输入角色名称"
                style={{ width: 300 }}
                onChange={(e) => setRoleName(e.target.value)}
              />
            </Space>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={resetSearch}>重置</Button>
            </Space>
          </div>
          {roleJudgment(userInfo, 'ROLE_EDIT') && (
            <div
              style={{
                marginBottom: 16,
              }}
            >
              <Button type="primary" icon={<PlusOutlined />} onClick={() => router.push('/system/role/add')}>
                新增角色
              </Button>
            </div>
          )}
          <PublicTable
            ref={publicTableRef}
            columns={columns}
            url={'/ai-manage/v1/role/list'}
            rowKey={'id'}
            defaultQuery={defaultQuery}
          />
        </Card>
      </div>
    </Layout>
  );
};

export default RolePage;

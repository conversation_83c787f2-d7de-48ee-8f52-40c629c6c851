'use client';
import React, { useState, useEffect } from 'react';
import { Modal, Button, Table, Input, Form, Space, Popconfirm, message } from 'antd';
import { updateFunction } from '@/services/menu/api';
import { FunctionAuthoritys, ListDataType } from '@/types/menu/index';

interface SetPermissionProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  currentData: ListDataType;
}

const SetPermission: React.FC<SetPermissionProps> = ({ open, onCancel, onSuccess, currentData }) => {
  const [dataSource, setDataSource] = useState<FunctionAuthoritys[]>([]);

  const columns = [
    {
      title: '功能名称',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: FunctionAuthoritys) => (
        <Input
          placeholder="请输入..."
          value={text}
          onChange={(e) => handleInputChange(record.orderNo!, 'title', e.target.value)}
        />
      ),
    },
    {
      title: '功能编码',
      dataIndex: 'code',
      key: 'code',
      render: (text: string, record: FunctionAuthoritys) => (
        <Input
          placeholder="请输入..."
          value={text}
          onChange={(e) => handleInputChange(record.orderNo!, 'code', e.target.value)}
        />
      ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      render: (text: string, record: FunctionAuthoritys) => (
        <Input
          placeholder="请输入..."
          value={text}
          onChange={(e) => handleInputChange(record.orderNo!, 'remark', e.target.value)}
        />
      ),
    },
    {
      title: '排序',
      dataIndex: 'orderNo',
      key: 'orderNo',
      width: 100,
      render: (text: number, record: FunctionAuthoritys) => (
        <Input
          type="number"
          placeholder="1"
          disabled
          value={text}
          onChange={(e) => handleInputChange(record.orderNo!, 'orderNo', Number(e.target.value))}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: FunctionAuthoritys) => (
        <Popconfirm
          title="提示"
          description="确定要删除这条数据？"
          onConfirm={() => confirm(record.orderNo as number)}
          okText="确认"
          cancelText="取消"
        >
          <Button danger type="link">
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];

  const confirm = (orderNo: number) => {
    message.success('删除成功');
    setDataSource((prev) => prev.filter((item) => item.orderNo !== orderNo));
  };

  const handleInputChange = (orderNo: number, field: string, value: any) => {
    setDataSource((prev) => prev.map((item) => (item.orderNo === orderNo ? { ...item, [field]: value } : item)));
  };

  const handleAdd = () => {
    const newKey = (dataSource[dataSource.length - 1]?.key || 0) + 1;
    setDataSource([
      ...dataSource,
      {
        key: null,
        title: '',
        code: '',
        remark: '',
        orderNo: dataSource.length + 1,
      },
    ]);
  };

  const handleOk = async () => {
    const { success, msg } = await updateFunction({
      functionAuthoritys: dataSource,
      parent: currentData.key,
    });
    if (success) {
      message.success('保存成功');
      onSuccess();
    } else {
      message.error(msg || '保存失败');
    }
    onSuccess();
  };

  useEffect(() => {
    if (!open) {
      setDataSource([]); // 重置表格数据
    }
  }, [open]);

  useEffect(() => {
    if (open && currentData) {
      setDataSource(currentData.functionChildren || []);
    }
  }, [currentData, open]);

  return (
    <Modal
      title="功能权限管理"
      open={open}
      onCancel={onCancel}
      width={1000}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk}>
          确定
        </Button>,
      ]}
    >
      <Button key="add" type="primary" onClick={handleAdd} style={{ marginBottom: 16 }}>
        新增
      </Button>
      <Table rowKey={'orderNo'} dataSource={dataSource} columns={columns} pagination={false} bordered />
    </Modal>
  );
};

export default SetPermission;

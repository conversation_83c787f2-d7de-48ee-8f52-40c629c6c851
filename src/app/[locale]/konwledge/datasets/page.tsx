'use client';
// 导入必要的依赖
import { useState, useEffect, useCallback } from 'react';
import { Card, Input, Button, Table, Space, message, Typography, Modal } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { SearchOutlined, PlusOutlined, FileTextOutlined } from '@ant-design/icons';
import Layout from '@/components/Layout';
import AddDatasetsModal from './components/addDatasetsModal';
import { useRouter } from 'next/navigation';
import zhCN from 'antd/es/locale/zh_CN';
import { del, getList } from '@/services/konwledge/api';
import debounce from 'lodash-es/debounce';
import dayjs from 'dayjs';
import { roleJudgment } from '@/utils/index';
import useStore from '@/store/useStore';
import styles from './page.module.less';
const { Title } = Typography;
const { confirm } = Modal;

// 定义知识库列表项的数据接口
interface ListTemplate {
  id: number;
  name: string;
  documentNum: string;
  content: string;
  createTime: string;
}
const KonwledgeFilePage: React.FC = () => {
  const router = useRouter();
  const { userInfo } = useStore();
  // 状态管理
  const [searchName, setSearchName] = useState<string>(''); // 搜索关键词
  const [loading, setLoading] = useState(false); // 加载状态
  const [data, setData] = useState<ListTemplate[]>([]); // 列表数据
  const [modalOpen, setModalOpen] = useState(false); // 模态框显示状态
  const [currentRecord, setCurrentRecord] = useState<Partial<ListTemplate>>(); // 当前操作的记录
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add'); // 模态框模式：新增/编辑
  const [current, setCurrent] = useState<number>(1); // 当前页码
  const [pageSize, setPageSize] = useState<number>(10); // 每页条数

  // 初始化加载
  useEffect(() => {
    handleSearch({ params: { name: searchName }, current, pageSize });
  }, [searchName, current, pageSize]);

  // 定义表格列配置
  const columns: ColumnsType<ListTemplate> = [
    {
      title: '知识库名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (_, record) => (
        <Space className={styles.name}>
          <div className={styles.icon}>
            <FileTextOutlined />
          </div>
          <div>
            <p style={{ fontSize: 16, fontWeight: 600, margin: 0 }}>{record.name}</p>
            <small style={{ color: '#999' }}>{record.content}</small>
          </div>
        </Space>
      ),
    },
    {
      title: '文件数量',
      dataIndex: 'documentNum',
      key: 'documentNum',
      width: 120,
      render: (_, record) => record.documentNum ?? 0,
    },
    {
      title: '描述',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      width: 120,
    },

    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 120,
      render: (_, record) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm') : '-'),
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: 100,
      render: (_, record) => (
        <Space size="middle">
          {roleJudgment(userInfo, 'KNOWLEDGE_SEE') && <a onClick={() => handleDetail(record)}>查看</a>}
          {roleJudgment(userInfo, 'KNOWLEDGE_EDIT') && <a onClick={() => handleEdit(record)}>编辑</a>}
          {roleJudgment(userInfo, 'KNOWLEDGE_DELETE') && (
            <a onClick={() => handleDelete(record)} style={{ color: '#ff4d4f' }}>
              删除
            </a>
          )}
        </Space>
      ),
    },
  ];

  // 搜索处理函数
  const handleSearch = async (params: any) => {
    try {
      setLoading(true);
      const response = await getList(params);
      if (response.success) {
        setData(response.resp[0].list);
      } else {
        message.error(response.msg || '查询失败');
      }
    } catch (error) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  // 搜索防抖处理
  const changeHandle = debounce((value: string) => {
    setSearchName(value);
  }, 500);

  // 新增知识库
  const handleAdd = () => {
    setModalOpen(true);
    setModalMode('add');
    setCurrentRecord(undefined);
  };

  // 查看知识库详情
  const handleDetail = (record: ListTemplate) => {
    router.push(`/konwledge/datasets/${record.id}?name=${record.name}&content=${record.content}`);
  };

  // 编辑知识库
  const handleEdit = (record: ListTemplate) => {
    setCurrentRecord(record);
    setModalMode('edit');
    setModalOpen(true);
  };

  // 删除知识库
  const handleDelete = (record: ListTemplate) => {
    confirm({
      title: '确认删除',
      content: `是否删除该文件：${record.name}?`,
      okText: '确认',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      async onOk() {
        try {
          const response = await del(record.id);
          if (response.success) {
            message.success('删除成功');
            handleSearch({ params: { name: searchName }, current, pageSize });
          } else {
            message.error(response.msg || '删除失败');
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 模态框操作成功后的回调
  const handleModalSuccess = () => {
    setModalOpen(false);
    handleSearch({ params: { name: searchName }, current, pageSize });
  };

  // 分页变化处理
  const PageChange = (page: number, pageSize: number) => {
    setCurrent(page);
    setPageSize(pageSize);
  };

  return (
    <Layout curActive="/konwledge/datasets">
      <div>
        <Card variant="borderless">
          {/* 搜索和操作区域 */}
          <div
            style={{
              marginBottom: 24,
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Input
              style={{ width: 250 }}
              size="middle"
              placeholder="搜索知识库"
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              prefix={<SearchOutlined />}
              allowClear
            />
            {/* 根据权限显示创建按钮 */}
            {roleJudgment(userInfo, 'KNOWLEDGE_ADD') && (
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                创建知识库
              </Button>
            )}
          </div>
          {/* 知识库列表表格 */}
          <Table
            columns={columns}
            dataSource={data}
            loading={loading}
            rowKey="id"
            scroll={{ x: 1300 }}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
              defaultPageSize: 10,
              locale: zhCN.Pagination,
              pageSize,
              onChange: PageChange,
              current,
              onShowSizeChange: PageChange,
            }}
          />
        </Card>

        {/* 新增/编辑知识库的模态框 */}
        <AddDatasetsModal
          open={modalOpen}
          onCancel={() => setModalOpen(false)}
          onSuccess={handleModalSuccess}
          initialValues={currentRecord}
          mode={modalMode}
        />
      </div>
    </Layout>
  );
};

export default KonwledgeFilePage;

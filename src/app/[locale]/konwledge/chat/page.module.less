.chat-container {
    display: flex;
    width: 100%;
    height: 100%;
    position: relative;

    .conversations:not(.is-open) {
        width: 0;
        opacity: 0;
        flex: 0 0 0;
    }

    .conversations.is-open {
        overflow: hidden;
        /* 确保内容不溢出 */
        white-space: nowrap;
        /* 防止文本换行 */
        flex: 1 1 auto;
        /* 当侧边栏打开时，占据可用空间 */
    }

    .conversations {
        width: 230px;
        /* 初始宽度 */
        max-width: 230px;
        border-right: 1px solid #EDF0F1;
        overflow: hidden;
        /* 确保内容不溢出 */
        max-height: 100%;
        background-color: #FAFCFD;

        & .actions {
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            z-index: 9;
            border-bottom: 1px solid #EDF0F1;
            .close {
                font-size: 1.2rem;
                width: 2.5rem;
                height: 2.5rem;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 8px;
                color: #42484A;
                cursor: pointer;
                // border: 1px solid #f00;

                &:hover {
                   background-color: #EDF0F1;
                }
            }
        }

        .conversation-list {
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            max-height: 100%;

            .conversation {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;
                cursor: pointer;
                width: 100%;
                user-select: none;
                transition: background-color 0.2s ease-in-out;

                &__title {
                    color: #616161;
                    white-space: nowrap;
                    /* 禁止换行 */
                    overflow: hidden;
                    /* 超出部分隐藏 */
                    text-overflow: ellipsis;
                    /* 显示省略号 */
                }

                &__delete {
                    display: none;
                    color: #A7ACAD;
                    transition: all 0.2s ease-in-out;

                    &:hover {
                        color: #F93A37;
                        background-color: #EEE;
                    }
                }

                &.active {
                    border-right: 3px solid #2C86A8;
                    padding-right: 13px;
                    background-color: #EFF1F2;

                    & .conversation__title {
                        color: #000000;
                    }
                }

                &:not(.active):hover {
                    background-color: #EDF0F1;

                    & .conversation__delete {
                        display: block;
                    }
                }

            }
        }

        .conversation-list::-webkit-scrollbar {
            position: absolute;
            width: 4px;
        }

        .conversation-list::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 4px;
        }

        .conversation-list::-webkit-scrollbar-thumb {
            background: #C4C7C8;
            border-radius: 4px;
        }

        .conversation-list::-webkit-scrollbar-thumb:hover {
            background: rgb(100, 100, 100);
            border-radius: 4px;
        }

        .conversation-list::-webkit-scrollbar-thumb:active {
            background: rgb(68, 68, 68);
            border-radius: 4px;
        }


    }

    @media (max-width: 520px) {
        .conversations {
            position: absolute;
            z-index: 101;
            width: 300px;
            height: 100%;
            border-radius: 0 16px 16px 0;
            box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.05);
        }
    }

    .chat {
        position: relative;
        width: 100%;
        max-height: 100vh;
        display: flex;
        flex-direction: column;
        overflow-x: hidden;
        background: white;
        position: relative;
        box-sizing: border-box;
        flex: 5 5 200px;
        overflow-y: scroll;

        .chat-header {
            user-select: none;
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: white;
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            .header__left,
            .header__right {
                display: flex;
                align-items: center;
            }

            .header__left {
                .nav-btn {
                    height: 2.5rem;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 8px;
                    color:  #212729;
                    cursor: pointer;
                    font-size: 1rem;
                    width: auto;
                    padding: 0.5rem 1rem;
                    .text {
                        margin-left: 10px;
                    }

                    &:hover {
                        background-color: #EDF0F1;
                    }
                }
            }
        }
    }
}
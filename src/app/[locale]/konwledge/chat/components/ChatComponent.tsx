'use client';

import React from 'react';
import styles from '../page.module.less';
import { MenuOutlined, PlusOutlined } from '@ant-design/icons';
import type { StateIprops } from '../page'; // 引用你定义的类型

interface ChatComponentProps {
  state: StateIprops;
  setState: React.Dispatch<React.SetStateAction<StateIprops>>;
}

const ChatComponent: React.FC<ChatComponentProps> = ({ state, setState }) => {
  return (
    <div className={styles['chat']}>
      <div className={styles['chat-header']}>
        <div className={styles['header__left']}>
          {!state.isSidebarOpen && (
            <div
              onClick={() => setState({ ...state, isSidebarOpen: true })}
              className={`${styles['nav-btn']} ${styles['close']}`}
            >
              <MenuOutlined />
            </div>
          )}
          <div className={`${styles['newchat']} ${styles['nav-btn']}`}>
            <PlusOutlined /> <span className={styles['text']}>新对话</span>
          </div>
        </div>
        <div className={styles['header__right']}>456456</div>
      </div>
      <div className={styles['chat-examples']}></div>
    </div>
  );
};

export default ChatComponent;

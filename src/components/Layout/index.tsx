'use client';
import React, { useState, useEffect } from 'react';
import { Layout, Menu, theme, Avatar, Dropdown, ConfigProvider, type MenuProps } from 'antd';
import {
  DesktopOutlined,
  FileTextOutlined,
  LineChartOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
} from '@ant-design/icons';
import { AiOutlineDeploymentUnit } from 'react-icons/ai';
import { GrDocumentConfig } from 'react-icons/gr';
import {
  IoDocumentTextOutline,
  IoFileTrayOutline,
  IoMicCircleOutline,
  IoHeartCircleOutline,
  IoIdCardOutline,
  IoJournalOutline,
} from 'react-icons/io5';
import { HiOutlineClipboardDocumentList } from 'react-icons/hi2';
import { TbTemplate } from 'react-icons/tb';
import { LiaBuromobelexperte } from 'react-icons/lia';
import { RiUserCommunityLine } from 'react-icons/ri';
import { LuServerCog } from 'react-icons/lu';
import { CgMenuGridO } from 'react-icons/cg';
import { getThemeBg } from '@/utils';
import cloneDeep from 'lodash-es/cloneDeep';
import { usePathname, useRouter, useParams, useSearchParams } from 'next/navigation';
import { logout } from '@/services/user/api';
import useStore from '@/store/useStore';
import { UserOutlined } from '@ant-design/icons';
import { BsRobot } from 'react-icons/bs';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import styles from './index.module.less';

// 设置 dayjs 语言为中文
dayjs.locale('zh-cn');

const { Header, Content, Footer, Sider } = Layout;

const iconMap: { [key: string]: React.ReactNode } = {
  DesktopOutlined: <DesktopOutlined />,
  AiOutlineDeploymentUnit: <AiOutlineDeploymentUnit />,
  GrDocumentConfig: <GrDocumentConfig />,
  IoDocumentTextOutline: <IoDocumentTextOutline />,
  HiOutlineClipboardDocumentList: <HiOutlineClipboardDocumentList />,
  TbTemplate: <TbTemplate />,
  LiaBuromobelexperte: <LiaBuromobelexperte />,
  RiUserCommunityLine: <RiUserCommunityLine />,
  LuServerCog: <LuServerCog />,
  CgMenuGridO: <CgMenuGridO />,
  IoFileTrayOutline: <IoFileTrayOutline />,
  IoMicCircleOutline: <IoMicCircleOutline />,
  IoHeartCircleOutline: <IoHeartCircleOutline />,
  IoIdCardOutline: <IoIdCardOutline />,
  IoJournalOutline: <IoJournalOutline />,
  FileTextOutlined: <FileTextOutlined />,
  LineChartOutlined: <LineChartOutlined />,
};

interface IProps {
  children: React.ReactNode;
  curActive: string;
  defaultOpen?: string[];
  outerStyle?: React.CSSProperties;
  agentDetailDom?: (collapsed: boolean) => React.ReactNode;
}

interface SideMenuItem {
  key: string;
  icon?: React.ReactNode;
  label: string;
  children?: SideMenuItem[];
}

const onLogout = () => {
  localStorage.removeItem('isDarkTheme');
  logout().then((res) => {
    res.success && (globalThis.location.href = '/login');
  });
};

const items: MenuProps['items'] = [
  {
    key: '1',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="#">
        个人中心
      </a>
    ),
  },
  {
    key: '2',
    label: (
      <a target="_blank" rel="noopener noreferrer" href="#">
        {'切换账户'} {/* 修复：将字符串包裹在模板字符串中 */}
      </a>
    ),
  },
  {
    key: '3',
    label: <span onClick={onLogout}>退出登录</span>,
  },
];

const CommonLayout: React.FC<IProps> = ({ children, curActive, outerStyle, defaultOpen = ['/'], agentDetailDom }) => {
  const { token } = theme.useToken();

  // console.log(token, 'opopo');
  // debugger;
  const [collapsed, setCollapsed] = useState(false);
  const [navList, setNavList] = useState<SideMenuItem[]>([]);

  const router = useRouter();
  const pathname = usePathname();
  const appId = useParams().appId;
  const searchParams = useSearchParams();
  // 获取查询参数
  const source = searchParams.get('source');
  const applyType = searchParams.get('applyType');
  const { userInfo, menuList, getMenuList, getUserInfo } = useStore();

  const [curTheme, setCurTheme] = useState<boolean>(false);
  const [sideMenu, setSideMenu] = useState<SideMenuItem[]>([]);

  /**
   * 特殊处理智能体调配菜单的名称：applyType==chat,名称为“对话”
   * applyType==chat,名称为“对话”
   * applyType==workflow && source==dify，名称为“调试”
   * applyType==workflow && source==inter，名称为“编排”
   */
  const setMenuName = () => {
    if (applyType === 'chat') {
      return '对话';
    }
    if (applyType === 'workflow' && source === 'dify') {
      return '调试';
    }
    if (applyType === 'workflow' && source === 'inter') {
      return '编排';
    }
  };

  //给menuList添加key和label属性
  const addMenuKeyAndLabel = (menuList: any[]): any[] => {
    return menuList.map((item) => {
      return {
        ...item,
        key: item.path.includes('${appId}') ? item.path.replace(/\$\{appId\}/g, appId) : item.path,
        label: item.title.includes('${title}') ? setMenuName() : item.title,
        icon: iconMap[item.icon],
        children: item.children ? addMenuKeyAndLabel(item.children) : null,
      };
    });
  };

  const handleMenuClick = (row: { key: string }) => {
    const updatedMenu = navList.find((item) => item.key === row.key)?.children || [];
    setSideMenu(updatedMenu);
    if (row.key === '/agent') {
      //智能体特殊处理，它没有左侧二级菜单
      router.push(row.key);
    } else {
      router.push(updatedMenu[0].key);
    }
  };

  const handleSelect = (row: { key: string }) => {
    if (row.key.includes('http')) {
      window.open(row.key);
      return;
    }
    router.push(row.key);
  };

  const handleClick = ({ key }: { key: string }) => {
    handleSelect({ key });
  };
  const contentStyle = {
    height: 'calc(100vh - 94px)',
    overflow: 'auto',
    margin: '15px 16px 0',
  };
  useEffect(() => {
    const isDark = !!localStorage.getItem('isDarkTheme');
    setCurTheme(isDark);
  }, []);

  useEffect(() => {
    if (menuList.length === 0) {
      getMenuList();
    }
    if (Reflect.ownKeys(userInfo).length === 0) {
      getUserInfo();
    }
  }, []);

  useEffect(() => {
    // 获取当前路径的第一段作为主路径
    const mainPath = '/' + pathname.split('/')[2];

    // 查找匹配的顶级菜单
    const matchedNav = navList.find((item) => {
      // 检查当前路径是否与菜单项的key匹配
      // 或者是其子路径(以菜单项的key开头)
      return item.key === mainPath || pathname.startsWith(item.key);
    });

    // 设置侧边栏菜单
    const updatedMenu = matchedNav?.children || [];
    setSideMenu(updatedMenu);
  }, [pathname, navList]);

  useEffect(() => {
    if (!!menuList.length) {
      setNavList(addMenuKeyAndLabel(cloneDeep(menuList)));
    }
  }, [menuList]);

  return (
    <ConfigProvider
      theme={{
        algorithm: curTheme ? theme.darkAlgorithm : theme.defaultAlgorithm,
        cssVar: true,
        hashed: false,
        token: {},
      }}
      locale={zhCN}
    >
      <Layout>
        <Header style={{ padding: 0, backgroundColor: '#001529', display: 'flex' }}>
          <div className={styles.warper}>
            <div className={styles.logo}>
              <div className={styles.icon}>
                <BsRobot />
              </div>
              <span>智能体矩阵</span>
            </div>

            <ul className={styles.nav}>
              {navList.map((item) => {
                return (
                  <li
                    className={`${pathname.startsWith(item.key) ? `${styles.active}` : ''} ${styles.item}`}
                    key={item.key}
                    onClick={() => handleMenuClick({ key: item.key })}
                  >
                    {item.label}
                  </li>
                );
              })}
            </ul>
            <div className={styles.right}>
              <div className={styles.avatar}>
                <Dropdown menu={{ items }} placement="bottomLeft" arrow>
                  <div>
                    <Avatar icon={<UserOutlined />} style={{ color: '#fff' }} size="large" />
                    <span style={{ color: '#fff' }}>{userInfo.userName}</span>
                  </div>
                </Dropdown>
              </div>
            </div>
          </div>
        </Header>
        <Layout>
          {
            //智能体没有左侧菜单
            pathname !== '/agent' && (
              <Sider
                trigger={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                theme={'dark'}
                breakpoint="lg"
                collapsedWidth="80"
                collapsible
                collapsed={collapsed}
                onCollapse={(value) => setCollapsed(value)}
              >
                {agentDetailDom && agentDetailDom(collapsed)}
                <Menu
                  theme={'dark'}
                  mode="inline"
                  defaultSelectedKeys={[curActive]}
                  items={sideMenu}
                  defaultOpenKeys={defaultOpen}
                  onSelect={handleSelect}
                  onClick={handleClick}
                />
              </Sider>
            )
          }

          <Content>
            <div
              style={{
                height: 'calc(100vh - 94px)',
                overflowY: 'auto',
                margin: '15px 16px 0',
                padding: 15,
                ...getThemeBg(curTheme),
                borderRadius: token.borderRadiusSM,
                ...outerStyle,
              }}
            >
              {children}
            </div>
          </Content>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default CommonLayout;

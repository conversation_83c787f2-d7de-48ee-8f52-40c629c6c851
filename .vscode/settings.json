{
    "files.encoding": "utf8",
    "files.autoGuessEncoding": false,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true,
    "search.followSymlinks": false,
    "stylelint.validate": [
        "css",
        "less"
    ],
    "workbench.editorAssociations": {
        "*.html": "default"
    },
    "workbench.colorCustomizations": {
        "[Atom One Dark Theme]": {
            "tab.activeBackground": "#d91414",
            "tab.activeForeground": "#c10909",
            "tab.unfocusedActiveBackground": "#1e9843",
            "tab.unfocusedActiveForeground": "#a40a0a"
        }
    },
    "javascript.validate.enable": true,
    "[javascript]": {
        "editor.defaultFormatter": "vscode.typescript-language-features"
    },
    "vetur.experimental.templateInterpolationService": true,
    "editor.formatOnPaste": false,
    "editor.formatOnType": false,
    "editor.suggestSelection": "first",
    "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue",
    "typescript.locale": "zh-CN",
    "diffEditor.ignoreTrimWhitespace": false,
    "liveServer.settings.CustomBrowser": "chrome",
    "editor.fontLigatures": false,
    "files.associations": {
        "*.vue": "vue",
        "manifest.json": "jsonc",
        "pages.json": "jsonc"
    },
    "vetur.validation.template": false,
    "[vue]": {
        "editor.defaultFormatter": "Vue.volar"
    },
    "[html]": {
        "editor.defaultFormatter": "Vue.volar"
    },
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "interview.updateNotification": 1679471773684,
    "interview.workspaceFolder": "/Volumes/工作/learn/前端每日一题",
    "editor.codeActionsOnSave": {
        "source.fixAll.stylelint": "explicit"
    },
    "tailwindCSS.emmetCompletions": true,
    "[less]": {
        "editor.defaultFormatter": "vscode.css-language-features"
    },
    "create-uniapp-view.directory": true,
    "create-uniapp-view.template": "vue3",
    "create-uniapp-view.style": "scss",
    "create-uniapp-view.name": "与文件夹同名",
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "npm.keybindingsChangedWarningShown": true,
    "editor.bracketPairColorization.independentColorPoolPerBracketType": true,
    "editor.guides.bracketPairs": true,
    "prisma.showPrismaDataPlatformNotification": false,
    "editor.formatOnSave": true,
    "Codegeex.License": "",
    "Codegeex.Privacy": true,
    "[typescriptreact]": {
        "editor.defaultFormatter": "vscode.typescript-language-features"
    },
    "stylelint.config": {},
}

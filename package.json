{"name": "next-admin", "version": "1.0.0", "private": true, "author": "alex xu", "description": "An enterprise-grade middle and back office solution based on ANTD and NEXTJS", "keywords": ["ant", "next.js", "components", "next-admin", "admin stystem", "frontend", "react", "react-component"], "homepage": "http://next-admin.com", "bugs": {"url": "https://github.com/MrXujiang/next-admin/issues"}, "repository": {"type": "git", "url": "https://github.com/MrXujiang/next-admin"}, "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=test next dev -p 80", "dev:test": "cross-env NODE_ENV=test next dev -p 80", "dev:prod": "cross-env NODE_ENV=production next dev -p 80", "build": "next build ", "start": "next start -p 8989", "deploy:test": "pnpm build:test && pm2 start pm2.config.js --env test", "deploy:prod": "pnpm build:prod && pm2 start pm2.config.js --env prod", "build:test": "cross-env NODE_ENV=test next build && npm run move:static && npm run move:public", "build:prod": "cross-env NODE_ENV=production next build && npm run move:static && npm run move:public", "move:static": "cp -R .next/static .next/standalone/.next/static", "move:public": "cp -R public .next/standalone/public", "start:standalone": "PORT=12000 node server.js", "lint": "next lint", "lint2": "eslint src --fix --ext .ts,.tsx,.js,.jsx --max-warnings 0", "format:check": "prettier --check \"src/**/*.{js,ts,tsx}\"", "format": "prettier --write  \"src/**/*.{js,ts,tsx}\" ", "lint:css": "stylelint '**/*.less' --cache --fix", "lint:all": "npm run lint2 && npm run lint:css"}, "dependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/nextjs-registry": "^1.0.0", "@ant-design/pro-chat": "^1.15.3", "@antv/g-plugin-rough-canvas-renderer": "^2.0.8", "@antv/g2": "^5.2.0", "@antv/g6": "^4.8.21", "@bytemd/plugin-gfm": "^1.21.0", "@bytemd/react": "^1.21.0", "@daybrush/utils": "^1.13.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@fortune-sheet/react": "^0.19.7", "@latelyjs/fetch-event-source": "^0.0.4", "@moveable/helper": "^0.1.3", "@rangermauve/fetch-event-source": "^1.0.3", "antd": "^5.24.4", "antd-style": "^3.7.1", "axios": "^1.6.8", "butterfly-dag": "5.1.0-beta.38", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "d3-scale-chromatic": "^3.1.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "echarts-for-react": "^3.0.2", "emoji-mart": "^5.6.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "next": "14.1.3", "next-intl": "^3.9.4", "react": "^18", "react-dom": "^18", "react-draggable": "^4.4.6", "react-drawio": "^0.1.8", "react-icons": "^5.5.0", "react-keycon": "^0.3.0", "react-moveable": "^0.56.0", "react-selecto": "^1.26.3", "sharp": "^0.33.3", "sortablejs": "^1.15.2", "webfontloader": "^1.6.28", "zustand": "^5.0.3"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/d3-scale-chromatic": "^3.0.3", "@types/jsonwebtoken": "^9.0.6", "@types/next": "^9.0.0", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18.3.19", "@types/react-dom": "^18", "@types/sortablejs": "^1.15.8", "@types/webfontloader": "^1.6.38", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "autoprefixer": "^10.0.1", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.1.3", "eslint-config-prettier": "^8.10.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "less": "4.2.0", "less-loader": "11.1.3", "next-plugin-antd-less": "^1.8.0", "postcss": "^8.5.3", "postcss-less": "^6.0.0", "prettier": "^3.2.5", "sass": "^1.89.2", "stylelint": "^16.0.2", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-less": "^3.0.1", "stylelint-order": "6.0.4", "typescript": "^5.8.3"}, "lint-staged": {"*.{ts,tsx}": ["eslint src --fix --ext .ts,.tsx,.js,.jsx --max-warnings 0"], "*.less": ["stylelint --fix"]}}